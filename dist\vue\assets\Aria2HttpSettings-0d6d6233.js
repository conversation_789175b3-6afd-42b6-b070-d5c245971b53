import{d as A,x as S,r as x,a as _,L as B,E as u,g as R,l as a,y as E,f as I,k as s,e as d,B as N,o as V,m as l,u as h,p as T,i as D}from"./index-a49529e1.js";const F={class:"http-settings"},G=A({__name:"Aria2HttpSettings",setup(M){const p=S(),w=x(),f=x(!1),m=x(!1),e=_({userAgent:"",httpProxy:"",httpsProxy:"",httpProxyUser:"",httpProxyPasswd:"",httpUser:"",httpPasswd:"",checkCertificate:!0,caCertificate:"",header:"",cookie:"",referer:"",connectTimeout:60,timeout:60});B(()=>{p.isConnected&&c()});async function c(){if(!p.isConnected){u.warning("请先连接到 Aria2 服务器");return}f.value=!0;try{const o=await p.getGlobalOptions();o&&(e.userAgent=o["user-agent"]||"",e.httpProxy=o["http-proxy"]||"",e.httpsProxy=o["https-proxy"]||"",e.httpProxyUser=o["http-proxy-user"]||"",e.httpProxyPasswd=o["http-proxy-passwd"]||"",e.httpUser=o["http-user"]||"",e.httpPasswd=o["http-passwd"]||"",e.checkCertificate=o["check-certificate"]!=="false",e.caCertificate=o["ca-certificate"]||"",e.header=o.header||"",e.cookie=o.cookie||"",e.referer=o.referer||"",e.connectTimeout=parseInt(o["connect-timeout"]||"60"),e.timeout=parseInt(o.timeout||"60")),u.success("设置加载成功")}catch(o){u.error("加载设置失败"),console.error("Failed to load HTTP settings:",o)}finally{f.value=!1}}async function v(){if(!p.isConnected){u.warning("请先连接到 Aria2 服务器");return}m.value=!0;try{const o={};e.userAgent&&(o["user-agent"]=e.userAgent),e.httpProxy&&(o["http-proxy"]=e.httpProxy),e.httpsProxy&&(o["https-proxy"]=e.httpsProxy),e.httpProxyUser&&(o["http-proxy-user"]=e.httpProxyUser),e.httpProxyPasswd&&(o["http-proxy-passwd"]=e.httpProxyPasswd),e.httpUser&&(o["http-user"]=e.httpUser),e.httpPasswd&&(o["http-passwd"]=e.httpPasswd),o["check-certificate"]=e.checkCertificate?"true":"false",e.caCertificate&&(o["ca-certificate"]=e.caCertificate),e.header&&(o.header=e.header),e.cookie&&(o.cookie=e.cookie),e.referer&&(o.referer=e.referer),o["connect-timeout"]=e.connectTimeout.toString(),o.timeout=e.timeout.toString(),await p.changeGlobalOptions(o),u.success("设置保存成功")}catch(o){u.error("保存设置失败"),console.error("Failed to save HTTP settings:",o)}finally{m.value=!1}}function C(){e.userAgent="",e.httpProxy="",e.httpsProxy="",e.httpProxyUser="",e.httpProxyPasswd="",e.httpUser="",e.httpPasswd="",e.checkCertificate=!0,e.caCertificate="",e.header="",e.cookie="",e.referer="",e.connectTimeout=60,e.timeout=60,u.info("已重置为默认值")}return(o,t)=>{const n=d("el-input"),i=d("el-form-item"),P=d("el-card"),U=d("el-switch"),g=d("el-input-number"),y=d("el-button"),k=d("el-space"),H=d("el-form"),b=N("loading");return V(),R("div",F,[t[32]||(t[32]=a("div",{class:"settings-header"},[a("h2",null,"HTTP/HTTPS 设置"),a("p",{class:"settings-description"},"配置 HTTP 和 HTTPS 下载相关参数")],-1)),E((V(),I(H,{ref_key:"formRef",ref:w,model:e,"label-width":"200px",style:{"max-width":"800px"}},{default:s(()=>[l(P,{class:"setting-group"},{header:s(()=>[...t[14]||(t[14]=[a("span",{class:"group-title"},"连接设置",-1)])]),default:s(()=>[l(i,{label:"用户代理"},{default:s(()=>[l(n,{modelValue:e.userAgent,"onUpdate:modelValue":t[0]||(t[0]=r=>e.userAgent=r),placeholder:"默认用户代理字符串"},null,8,["modelValue"]),t[15]||(t[15]=a("div",{class:"form-tip"},"设置 HTTP 请求的 User-Agent 头",-1))]),_:1}),l(i,{label:"HTTP 代理"},{default:s(()=>[l(n,{modelValue:e.httpProxy,"onUpdate:modelValue":t[1]||(t[1]=r=>e.httpProxy=r),placeholder:"http://proxy.example.com:8080"},null,8,["modelValue"]),t[16]||(t[16]=a("div",{class:"form-tip"},"HTTP 代理服务器地址",-1))]),_:1}),l(i,{label:"HTTPS 代理"},{default:s(()=>[l(n,{modelValue:e.httpsProxy,"onUpdate:modelValue":t[2]||(t[2]=r=>e.httpsProxy=r),placeholder:"https://proxy.example.com:8080"},null,8,["modelValue"]),t[17]||(t[17]=a("div",{class:"form-tip"},"HTTPS 代理服务器地址",-1))]),_:1}),l(i,{label:"代理用户名"},{default:s(()=>[l(n,{modelValue:e.httpProxyUser,"onUpdate:modelValue":t[3]||(t[3]=r=>e.httpProxyUser=r),placeholder:"代理服务器用户名"},null,8,["modelValue"])]),_:1}),l(i,{label:"代理密码"},{default:s(()=>[l(n,{modelValue:e.httpProxyPasswd,"onUpdate:modelValue":t[4]||(t[4]=r=>e.httpProxyPasswd=r),type:"password",placeholder:"代理服务器密码","show-password":""},null,8,["modelValue"])]),_:1})]),_:1}),l(P,{class:"setting-group"},{header:s(()=>[...t[18]||(t[18]=[a("span",{class:"group-title"},"认证设置",-1)])]),default:s(()=>[l(i,{label:"HTTP 用户名"},{default:s(()=>[l(n,{modelValue:e.httpUser,"onUpdate:modelValue":t[5]||(t[5]=r=>e.httpUser=r),placeholder:"HTTP 基本认证用户名"},null,8,["modelValue"])]),_:1}),l(i,{label:"HTTP 密码"},{default:s(()=>[l(n,{modelValue:e.httpPasswd,"onUpdate:modelValue":t[6]||(t[6]=r=>e.httpPasswd=r),type:"password",placeholder:"HTTP 基本认证密码","show-password":""},null,8,["modelValue"])]),_:1}),l(i,{label:"接受所有证书"},{default:s(()=>[l(U,{modelValue:e.checkCertificate,"onUpdate:modelValue":t[7]||(t[7]=r=>e.checkCertificate=r)},null,8,["modelValue"]),t[19]||(t[19]=a("div",{class:"form-tip"},"是否验证 HTTPS 证书",-1))]),_:1}),l(i,{label:"CA 证书文件"},{default:s(()=>[l(n,{modelValue:e.caCertificate,"onUpdate:modelValue":t[8]||(t[8]=r=>e.caCertificate=r),placeholder:"CA 证书文件路径"},null,8,["modelValue"]),t[20]||(t[20]=a("div",{class:"form-tip"},"用于验证 HTTPS 证书的 CA 证书文件",-1))]),_:1})]),_:1}),l(P,{class:"setting-group"},{header:s(()=>[...t[21]||(t[21]=[a("span",{class:"group-title"},"请求设置",-1)])]),default:s(()=>[l(i,{label:"请求头"},{default:s(()=>[l(n,{modelValue:e.header,"onUpdate:modelValue":t[9]||(t[9]=r=>e.header=r),type:"textarea",rows:4,placeholder:"自定义 HTTP 请求头，每行一个，格式：Header: Value"},null,8,["modelValue"]),t[22]||(t[22]=a("div",{class:"form-tip"},"自定义 HTTP 请求头，每行一个",-1))]),_:1}),l(i,{label:"Cookie"},{default:s(()=>[l(n,{modelValue:e.cookie,"onUpdate:modelValue":t[10]||(t[10]=r=>e.cookie=r),placeholder:"HTTP Cookie 字符串"},null,8,["modelValue"]),t[23]||(t[23]=a("div",{class:"form-tip"},"发送给服务器的 Cookie",-1))]),_:1}),l(i,{label:"Referer"},{default:s(()=>[l(n,{modelValue:e.referer,"onUpdate:modelValue":t[11]||(t[11]=r=>e.referer=r),placeholder:"HTTP Referer 头"},null,8,["modelValue"]),t[24]||(t[24]=a("div",{class:"form-tip"},"HTTP Referer 头的值",-1))]),_:1}),l(i,{label:"连接超时"},{default:s(()=>[l(g,{modelValue:e.connectTimeout,"onUpdate:modelValue":t[12]||(t[12]=r=>e.connectTimeout=r),min:1,max:600,style:{width:"200px"}},null,8,["modelValue"]),t[25]||(t[25]=a("span",{style:{"margin-left":"8px"}},"秒",-1)),t[26]||(t[26]=a("div",{class:"form-tip"},"建立连接的超时时间",-1))]),_:1}),l(i,{label:"请求超时"},{default:s(()=>[l(g,{modelValue:e.timeout,"onUpdate:modelValue":t[13]||(t[13]=r=>e.timeout=r),min:1,max:600,style:{width:"200px"}},null,8,["modelValue"]),t[27]||(t[27]=a("span",{style:{"margin-left":"8px"}},"秒",-1)),t[28]||(t[28]=a("div",{class:"form-tip"},"HTTP 请求的超时时间",-1))]),_:1})]),_:1}),l(i,{style:{"margin-top":"24px"}},{default:s(()=>[l(k,null,{default:s(()=>[l(y,{type:"primary",onClick:v,disabled:!h(p).isConnected,loading:m.value},{default:s(()=>[...t[29]||(t[29]=[T(" 保存设置 ",-1)])]),_:1},8,["disabled","loading"]),l(y,{onClick:c,disabled:!h(p).isConnected},{default:s(()=>[...t[30]||(t[30]=[T(" 重新加载 ",-1)])]),_:1},8,["disabled"]),l(y,{onClick:C},{default:s(()=>[...t[31]||(t[31]=[T(" 重置为默认值 ",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[b,f.value]])])}}});const L=D(G,[["__scopeId","data-v-d19d894a"]]);export{L as default};
