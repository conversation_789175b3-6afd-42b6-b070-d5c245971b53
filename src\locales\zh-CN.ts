export default {
  // 通用
  common: {
    confirm: '确定',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    close: '关闭',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息'
  },

  // 应用标题
  app: {
    title: 'Aria2 Desktop',
    version: '版本'
  },

  // 连接状态
  connection: {
    connected: '已连接',
    disconnected: '未连接',
    connecting: '连接中...',
    connectionFailed: '连接失败'
  },

  // 菜单
  menu: {
    downloadTasks: '下载任务',
    downloadCompleted: '下载完成',
    generalSettings: '常规设置',
    aria2Settings: 'Aria2 设置',
    aria2Status: 'Aria2 状态'
  },

  // 任务状态
  taskStatus: {
    active: '正在下载',
    waiting: '等待中',
    paused: '已暂停',
    error: '错误',
    complete: '已完成',
    removed: '已删除'
  },

  // 任务操作
  taskActions: {
    start: '开始',
    pause: '暂停',
    delete: '删除',
    retry: '重试',
    openLocation: '打开位置',
    viewDetails: '查看详情'
  },

  // 任务列表
  taskList: {
    fileName: '文件名',
    size: '大小',
    progress: '进度',
    status: '状态',
    speed: '速度',
    actions: '操作',
    noTasks: '暂无任务',
    selectedTasks: '已选择 {count} 个任务'
  },

  // 设置页面
  settings: {
    general: {
      title: '常规设置',
      description: '配置应用程序的基本行为和外观',
      language: '语言',
      theme: '主题',
      lightTheme: '浅色主题',
      darkTheme: '深色主题',
      autoTheme: '跟随系统',
      refreshInterval: '自动刷新间隔',
      autoConnect: '启动时自动连接',
      minimizeToTray: '最小化到系统托盘',
      uiSettings: '界面设置',
      showStatusBar: '显示状态栏',
      showToolbar: '显示工具栏',
      defaultView: '默认视图',
      saveSettings: '保存设置',
      resetToDefault: '重置为默认',
      exportSettings: '导出设置',
      importSettings: '导入设置',
      settingsSaved: '设置已保存',
      settingsReset: '设置已重置为默认值',
      settingsExported: '设置已导出',
      settingsImported: '设置已导入',
      autoSaveFailed: '自动保存失败',
      languageChangeNotice: '语言设置将在重启应用后生效',
      refreshIntervalTip: '设置任务列表和统计信息的自动刷新频率',
      autoConnectTip: '应用启动时自动连接到上次使用的 Aria2 服务器',
      minimizeToTrayTip: '关闭窗口时最小化到系统托盘而不是退出程序'
    }
  },

  // 对话框
  dialogs: {
    deleteTask: {
      title: '删除任务',
      message: '确定要删除这个任务吗？',
      deleteFiles: '同时删除文件',
      confirmDelete: '确认删除',
      confirmMessage: '确定要删除所有设置为默认值吗？此操作不可撤销。'
    },
    importSettings: {
      title: '导入设置',
      placeholder: '请粘贴设置 JSON 内容...',
      import: '导入',
      inputRequired: '请输入设置内容',
      importFailed: '导入设置失败：{error}'
    }
  },

  // 消息提示
  messages: {
    taskDeleted: '任务已删除',
    taskStarted: '任务已开始',
    taskPaused: '任务已暂停',
    taskRetried: '任务已重试',
    locationOpened: '已打开文件位置',
    directoryOpened: '已打开目录',
    openLocationFailed: '打开位置失败',
    featureOnlyInDesktop: '此功能仅在桌面版中可用',
    noDirectoryInfo: '没有找到文件目录信息'
  },

  // 统计信息
  stats: {
    downloadSpeed: '下载速度',
    uploadSpeed: '上传速度',
    active: '活动',
    waiting: '等待',
    stopped: '已停止'
  },

  // 时间单位
  time: {
    seconds: '秒',
    minutes: '分钟',
    hours: '小时',
    days: '天'
  },

  // 文件大小单位
  size: {
    bytes: 'B',
    kilobytes: 'KB',
    megabytes: 'MB',
    gigabytes: 'GB',
    terabytes: 'TB'
  }
}
