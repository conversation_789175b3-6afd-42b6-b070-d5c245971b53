import{d as y,x as L,r as g,a as B,L as N,E as d,g as E,l as i,y as q,f as O,k as o,e as u,B as A,o as U,m as t,u as b,p as v,i as R}from"./index-a49529e1.js";const z={class:"metalink-settings"},I=y({__name:"Aria2MetalinkSettings",setup(D){const r=L(),w=g(),k=g(!1),f=g(!1),e=B({followMetalink:!0,metalinkServers:"",metalinkPreferredProtocol:"https,http,ftp",metalinkLanguage:"zh-CN,en-US",metalinkLocation:"CN,US",metalinkOs:"linux,windows",metalinkVersion:"",metalinkBaseUri:"",metalinkEnableUniqueProtocol:!0});N(()=>{r.isConnected&&V()});async function V(){if(!r.isConnected){d.warning("请先连接到 Aria2 服务器");return}k.value=!0;try{const n=await r.getGlobalOptions();n&&(e.followMetalink=n["follow-metalink"]!=="false",e.metalinkServers=n["metalink-servers"]||"",e.metalinkPreferredProtocol=n["metalink-preferred-protocol"]||"https,http,ftp",e.metalinkLanguage=n["metalink-language"]||"zh-CN,en-US",e.metalinkLocation=n["metalink-location"]||"CN,US",e.metalinkOs=n["metalink-os"]||"linux,windows",e.metalinkVersion=n["metalink-version"]||"",e.metalinkBaseUri=n["metalink-base-uri"]||"",e.metalinkEnableUniqueProtocol=n["metalink-enable-unique-protocol"]!=="false"),d.success("设置加载成功")}catch(n){d.error("加载设置失败"),console.error("Failed to load Metalink settings:",n)}finally{k.value=!1}}async function S(){if(!r.isConnected){d.warning("请先连接到 Aria2 服务器");return}f.value=!0;try{const n={"follow-metalink":e.followMetalink?"true":"false","metalink-preferred-protocol":e.metalinkPreferredProtocol,"metalink-language":e.metalinkLanguage,"metalink-location":e.metalinkLocation,"metalink-os":e.metalinkOs,"metalink-enable-unique-protocol":e.metalinkEnableUniqueProtocol?"true":"false"};e.metalinkServers&&(n["metalink-servers"]=e.metalinkServers),e.metalinkVersion&&(n["metalink-version"]=e.metalinkVersion),e.metalinkBaseUri&&(n["metalink-base-uri"]=e.metalinkBaseUri),await r.changeGlobalOptions(n),d.success("设置保存成功")}catch(n){d.error("保存设置失败"),console.error("Failed to save Metalink settings:",n)}finally{f.value=!1}}function M(){e.followMetalink=!0,e.metalinkServers="",e.metalinkPreferredProtocol="https,http,ftp",e.metalinkLanguage="zh-CN,en-US",e.metalinkLocation="CN,US",e.metalinkOs="linux,windows",e.metalinkVersion="",e.metalinkBaseUri="",e.metalinkEnableUniqueProtocol=!0,d.info("已重置为默认值")}return(n,l)=>{const c=u("el-switch"),s=u("el-form-item"),m=u("el-input"),C=u("el-card"),p=u("el-button"),P=u("el-space"),_=u("el-form"),x=A("loading");return U(),E("div",z,[l[22]||(l[22]=i("div",{class:"settings-header"},[i("h2",null,"Metalink 设置"),i("p",{class:"settings-description"},"配置 Metalink 协议相关参数")],-1)),q((U(),O(_,{ref_key:"formRef",ref:w,model:e,"label-width":"200px",style:{"max-width":"800px"}},{default:o(()=>[t(C,{class:"setting-group"},{header:o(()=>[...l[9]||(l[9]=[i("span",{class:"group-title"},"Metalink 设置",-1)])]),default:o(()=>[t(s,{label:"启用 Metalink"},{default:o(()=>[t(c,{modelValue:e.followMetalink,"onUpdate:modelValue":l[0]||(l[0]=a=>e.followMetalink=a)},null,8,["modelValue"]),l[10]||(l[10]=i("div",{class:"form-tip"},"启用 Metalink 支持",-1))]),_:1}),t(s,{label:"Metalink 服务器"},{default:o(()=>[t(m,{modelValue:e.metalinkServers,"onUpdate:modelValue":l[1]||(l[1]=a=>e.metalinkServers=a),type:"textarea",rows:4,placeholder:"Metalink 服务器列表，每行一个"},null,8,["modelValue"]),l[11]||(l[11]=i("div",{class:"form-tip"},"额外的 Metalink 服务器",-1))]),_:1}),t(s,{label:"首选服务器"},{default:o(()=>[t(m,{modelValue:e.metalinkPreferredProtocol,"onUpdate:modelValue":l[2]||(l[2]=a=>e.metalinkPreferredProtocol=a),placeholder:"http,https,ftp"},null,8,["modelValue"]),l[12]||(l[12]=i("div",{class:"form-tip"},"首选的协议，用逗号分隔",-1))]),_:1}),t(s,{label:"语言"},{default:o(()=>[t(m,{modelValue:e.metalinkLanguage,"onUpdate:modelValue":l[3]||(l[3]=a=>e.metalinkLanguage=a),placeholder:"zh-CN,en-US"},null,8,["modelValue"]),l[13]||(l[13]=i("div",{class:"form-tip"},"首选语言，用逗号分隔",-1))]),_:1}),t(s,{label:"位置"},{default:o(()=>[t(m,{modelValue:e.metalinkLocation,"onUpdate:modelValue":l[4]||(l[4]=a=>e.metalinkLocation=a),placeholder:"CN,US"},null,8,["modelValue"]),l[14]||(l[14]=i("div",{class:"form-tip"},"首选位置，用逗号分隔",-1))]),_:1}),t(s,{label:"操作系统"},{default:o(()=>[t(m,{modelValue:e.metalinkOs,"onUpdate:modelValue":l[5]||(l[5]=a=>e.metalinkOs=a),placeholder:"linux,windows"},null,8,["modelValue"]),l[15]||(l[15]=i("div",{class:"form-tip"},"首选操作系统，用逗号分隔",-1))]),_:1}),t(s,{label:"版本"},{default:o(()=>[t(m,{modelValue:e.metalinkVersion,"onUpdate:modelValue":l[6]||(l[6]=a=>e.metalinkVersion=a),placeholder:"1.0,2.0"},null,8,["modelValue"]),l[16]||(l[16]=i("div",{class:"form-tip"},"首选版本，用逗号分隔",-1))]),_:1}),t(s,{label:"基础 URI"},{default:o(()=>[t(m,{modelValue:e.metalinkBaseUri,"onUpdate:modelValue":l[7]||(l[7]=a=>e.metalinkBaseUri=a),placeholder:"基础 URI"},null,8,["modelValue"]),l[17]||(l[17]=i("div",{class:"form-tip"},"Metalink 文件的基础 URI",-1))]),_:1}),t(s,{label:"启用唯一协议"},{default:o(()=>[t(c,{modelValue:e.metalinkEnableUniqueProtocol,"onUpdate:modelValue":l[8]||(l[8]=a=>e.metalinkEnableUniqueProtocol=a)},null,8,["modelValue"]),l[18]||(l[18]=i("div",{class:"form-tip"},"每个协议只使用一个连接",-1))]),_:1})]),_:1}),t(s,{style:{"margin-top":"24px"}},{default:o(()=>[t(P,null,{default:o(()=>[t(p,{type:"primary",onClick:S,disabled:!b(r).isConnected,loading:f.value},{default:o(()=>[...l[19]||(l[19]=[v(" 保存设置 ",-1)])]),_:1},8,["disabled","loading"]),t(p,{onClick:V,disabled:!b(r).isConnected},{default:o(()=>[...l[20]||(l[20]=[v(" 重新加载 ",-1)])]),_:1},8,["disabled"]),t(p,{onClick:M},{default:o(()=>[...l[21]||(l[21]=[v(" 重置为默认值 ",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[x,k.value]])])}}});const G=R(I,[["__scopeId","data-v-c636f1c2"]]);export{G as default};
