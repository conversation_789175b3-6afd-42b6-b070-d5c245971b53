export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    close: 'Close',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info'
  },

  // App title
  app: {
    title: 'Aria2 Desktop',
    version: 'Version'
  },

  // Connection status
  connection: {
    connected: 'Connected',
    disconnected: 'Disconnected',
    connecting: 'Connecting...',
    connectionFailed: 'Connection Failed'
  },

  // Menu
  menu: {
    downloadTasks: 'Download Tasks',
    downloadCompleted: 'Download Completed',
    generalSettings: 'General Settings',
    aria2Settings: 'Aria2 Settings',
    aria2Status: 'Aria2 Status'
  },

  // Task status
  taskStatus: {
    active: 'Downloading',
    waiting: 'Waiting',
    paused: 'Paused',
    error: 'Error',
    complete: 'Completed',
    removed: 'Removed'
  },

  // Task actions
  taskActions: {
    start: 'Start',
    pause: 'Pause',
    delete: 'Delete',
    retry: 'Retry',
    openLocation: 'Open Location',
    viewDetails: 'View Details'
  },

  // Task list
  taskList: {
    fileName: 'File Name',
    size: 'Size',
    progress: 'Progress',
    status: 'Status',
    speed: 'Speed',
    actions: 'Actions',
    noTasks: 'No tasks',
    selectedTasks: '{count} tasks selected'
  },

  // Settings page
  settings: {
    general: {
      title: 'General Settings',
      description: 'Configure basic application behavior and appearance',
      language: 'Language',
      theme: 'Theme',
      lightTheme: 'Light Theme',
      darkTheme: 'Dark Theme',
      autoTheme: 'Follow System',
      refreshInterval: 'Auto Refresh Interval',
      autoConnect: 'Auto Connect on Startup',
      minimizeToTray: 'Minimize to System Tray',
      uiSettings: 'UI Settings',
      showStatusBar: 'Show Status Bar',
      showToolbar: 'Show Toolbar',
      defaultView: 'Default View',
      saveSettings: 'Save Settings',
      resetToDefault: 'Reset to Default',
      exportSettings: 'Export Settings',
      importSettings: 'Import Settings',
      settingsSaved: 'Settings saved',
      settingsReset: 'Settings reset to default',
      settingsExported: 'Settings exported',
      settingsImported: 'Settings imported',
      autoSaveFailed: 'Auto save failed',
      languageChangeNotice: 'Language settings will take effect after restarting the application',
      refreshIntervalTip: 'Set the auto refresh frequency for task list and statistics',
      autoConnectTip: 'Automatically connect to the last used Aria2 server on startup',
      minimizeToTrayTip: 'Minimize to system tray instead of exiting when closing the window'
    }
  },

  // Dialogs
  dialogs: {
    deleteTask: {
      title: 'Delete Task',
      message: 'Are you sure you want to delete this task?',
      deleteFiles: 'Also delete files',
      confirmDelete: 'Confirm Delete',
      confirmMessage: 'Are you sure you want to reset all settings to default? This action cannot be undone.'
    },
    importSettings: {
      title: 'Import Settings',
      placeholder: 'Please paste settings JSON content...',
      import: 'Import',
      inputRequired: 'Please enter settings content',
      importFailed: 'Import settings failed: {error}'
    }
  },

  // Messages
  messages: {
    taskDeleted: 'Task deleted',
    taskStarted: 'Task started',
    taskPaused: 'Task paused',
    taskRetried: 'Task retried',
    locationOpened: 'File location opened',
    directoryOpened: 'Directory opened',
    openLocationFailed: 'Failed to open location',
    featureOnlyInDesktop: 'This feature is only available in desktop version',
    noDirectoryInfo: 'No directory information found'
  },

  // Statistics
  stats: {
    downloadSpeed: 'Download Speed',
    uploadSpeed: 'Upload Speed',
    active: 'Active',
    waiting: 'Waiting',
    stopped: 'Stopped'
  },

  // Time units
  time: {
    seconds: 'seconds',
    minutes: 'minutes',
    hours: 'hours',
    days: 'days'
  },

  // File size units
  size: {
    bytes: 'B',
    kilobytes: 'KB',
    megabytes: 'MB',
    gigabytes: 'GB',
    terabytes: 'TB'
  }
}
