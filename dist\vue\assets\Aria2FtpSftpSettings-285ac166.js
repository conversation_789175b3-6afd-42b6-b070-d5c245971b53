import{d as M,x as R,r as P,a as A,L as K,E as f,g as B,l as r,y as I,f as E,k as l,e as p,B as N,o as V,m as s,u as v,p as c,i as D}from"./index-a49529e1.js";const G={class:"ftp-settings"},O=M({__name:"Aria2FtpSftpSettings",setup(L){const d=R(),T=P(),u=P(!1),m=P(!1),e=A({ftpUser:"",ftpPasswd:"",ftpProxy:"",ftpProxyUser:"",ftpProxyPasswd:"",ftpType:"binary",ftpPasv:!0,ftpReuseConnection:!0,sshHostKeyMd:"",connectTimeout:60});K(()=>{d.isConnected&&x()});async function x(){if(!d.isConnected){f.warning("请先连接到 Aria2 服务器");return}u.value=!0;try{const o=await d.getGlobalOptions();o&&(e.ftpUser=o["ftp-user"]||"",e.ftpPasswd=o["ftp-passwd"]||"",e.ftpProxy=o["ftp-proxy"]||"",e.ftpProxyUser=o["ftp-proxy-user"]||"",e.ftpProxyPasswd=o["ftp-proxy-passwd"]||"",e.ftpType=o["ftp-type"]||"binary",e.ftpPasv=o["ftp-pasv"]!=="false",e.ftpReuseConnection=o["ftp-reuse-connection"]!=="false",e.sshHostKeyMd=o["ssh-host-key-md"]||"",e.connectTimeout=parseInt(o["connect-timeout"]||"60")),f.success("设置加载成功")}catch(o){f.error("加载设置失败"),console.error("Failed to load FTP settings:",o)}finally{u.value=!1}}async function b(){if(!d.isConnected){f.warning("请先连接到 Aria2 服务器");return}m.value=!0;try{const o={"ftp-type":e.ftpType,"ftp-pasv":e.ftpPasv?"true":"false","ftp-reuse-connection":e.ftpReuseConnection?"true":"false","connect-timeout":e.connectTimeout.toString()};e.ftpUser&&(o["ftp-user"]=e.ftpUser),e.ftpPasswd&&(o["ftp-passwd"]=e.ftpPasswd),e.ftpProxy&&(o["ftp-proxy"]=e.ftpProxy),e.ftpProxyUser&&(o["ftp-proxy-user"]=e.ftpProxyUser),e.ftpProxyPasswd&&(o["ftp-proxy-passwd"]=e.ftpProxyPasswd),e.sshHostKeyMd&&(o["ssh-host-key-md"]=e.sshHostKeyMd),await d.changeGlobalOptions(o),f.success("设置保存成功")}catch(o){f.error("保存设置失败"),console.error("Failed to save FTP settings:",o)}finally{m.value=!1}}function F(){e.ftpUser="",e.ftpPasswd="",e.ftpProxy="",e.ftpProxyUser="",e.ftpProxyPasswd="",e.ftpType="binary",e.ftpPasv=!0,e.ftpReuseConnection=!0,e.sshHostKeyMd="",e.connectTimeout=60,f.info("已重置为默认值")}return(o,t)=>{const i=p("el-input"),a=p("el-form-item"),_=p("el-option"),U=p("el-select"),w=p("el-switch"),g=p("el-card"),S=p("el-input-number"),y=p("el-button"),C=p("el-space"),k=p("el-form"),H=N("loading");return V(),B("div",G,[t[18]||(t[18]=r("div",{class:"settings-header"},[r("h2",null,"FTP/SFTP 设置"),r("p",{class:"settings-description"},"配置 FTP 和 SFTP 协议相关参数")],-1)),I((V(),E(k,{ref_key:"formRef",ref:T,model:e,"label-width":"200px",style:{"max-width":"800px"}},{default:l(()=>[s(g,{class:"setting-group"},{header:l(()=>[...t[10]||(t[10]=[r("span",{class:"group-title"},"FTP 设置",-1)])]),default:l(()=>[s(a,{label:"FTP 用户名"},{default:l(()=>[s(i,{modelValue:e.ftpUser,"onUpdate:modelValue":t[0]||(t[0]=n=>e.ftpUser=n),placeholder:"FTP 用户名"},null,8,["modelValue"])]),_:1}),s(a,{label:"FTP 密码"},{default:l(()=>[s(i,{modelValue:e.ftpPasswd,"onUpdate:modelValue":t[1]||(t[1]=n=>e.ftpPasswd=n),type:"password",placeholder:"FTP 密码","show-password":""},null,8,["modelValue"])]),_:1}),s(a,{label:"FTP 代理"},{default:l(()=>[s(i,{modelValue:e.ftpProxy,"onUpdate:modelValue":t[2]||(t[2]=n=>e.ftpProxy=n),placeholder:"ftp://proxy.example.com:21"},null,8,["modelValue"])]),_:1}),s(a,{label:"FTP 代理用户名"},{default:l(()=>[s(i,{modelValue:e.ftpProxyUser,"onUpdate:modelValue":t[3]||(t[3]=n=>e.ftpProxyUser=n),placeholder:"代理用户名"},null,8,["modelValue"])]),_:1}),s(a,{label:"FTP 代理密码"},{default:l(()=>[s(i,{modelValue:e.ftpProxyPasswd,"onUpdate:modelValue":t[4]||(t[4]=n=>e.ftpProxyPasswd=n),type:"password",placeholder:"代理密码","show-password":""},null,8,["modelValue"])]),_:1}),s(a,{label:"FTP 类型"},{default:l(()=>[s(U,{modelValue:e.ftpType,"onUpdate:modelValue":t[5]||(t[5]=n=>e.ftpType=n),style:{width:"200px"}},{default:l(()=>[s(_,{label:"二进制",value:"binary"}),s(_,{label:"ASCII",value:"ascii"})]),_:1},8,["modelValue"])]),_:1}),s(a,{label:"被动模式"},{default:l(()=>[s(w,{modelValue:e.ftpPasv,"onUpdate:modelValue":t[6]||(t[6]=n=>e.ftpPasv=n)},null,8,["modelValue"]),t[11]||(t[11]=r("div",{class:"form-tip"},"使用 FTP 被动模式",-1))]),_:1}),s(a,{label:"重用连接"},{default:l(()=>[s(w,{modelValue:e.ftpReuseConnection,"onUpdate:modelValue":t[7]||(t[7]=n=>e.ftpReuseConnection=n)},null,8,["modelValue"]),t[12]||(t[12]=r("div",{class:"form-tip"},"重用 FTP 连接",-1))]),_:1})]),_:1}),s(g,{class:"setting-group"},{header:l(()=>[...t[13]||(t[13]=[r("span",{class:"group-title"},"SFTP 设置",-1)])]),default:l(()=>[s(a,{label:"SSH 主机密钥文件"},{default:l(()=>[s(i,{modelValue:e.sshHostKeyMd,"onUpdate:modelValue":t[8]||(t[8]=n=>e.sshHostKeyMd=n),placeholder:"SSH 主机密钥文件路径"},null,8,["modelValue"])]),_:1}),s(a,{label:"SFTP 连接超时"},{default:l(()=>[s(S,{modelValue:e.connectTimeout,"onUpdate:modelValue":t[9]||(t[9]=n=>e.connectTimeout=n),min:1,max:600,style:{width:"200px"}},null,8,["modelValue"]),t[14]||(t[14]=r("span",{style:{"margin-left":"8px"}},"秒",-1))]),_:1})]),_:1}),s(a,{style:{"margin-top":"24px"}},{default:l(()=>[s(C,null,{default:l(()=>[s(y,{type:"primary",onClick:b,disabled:!v(d).isConnected,loading:m.value},{default:l(()=>[...t[15]||(t[15]=[c(" 保存设置 ",-1)])]),_:1},8,["disabled","loading"]),s(y,{onClick:x,disabled:!v(d).isConnected},{default:l(()=>[...t[16]||(t[16]=[c(" 重新加载 ",-1)])]),_:1},8,["disabled"]),s(y,{onClick:F},{default:l(()=>[...t[17]||(t[17]=[c(" 重置为默认值 ",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[H,u.value]])])}}});const q=D(O,[["__scopeId","data-v-fda52399"]]);export{q as default};
