function o(t){return parseInt(t.totalLength)||0}function n(t){return parseInt(t.downloadSpeed)||0}function c(t){const e=parseInt(t.totalLength),a=parseInt(t.completedLength),r=parseInt(t.downloadSpeed);return r===0||a>=e?0:Math.round((e-a)/r)}function i(t){if(t===0)return"0 B";const e=1024,a=["B","KB","MB","GB","TB"],r=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,r)).toFixed(1))+" "+a[r]}function s(t){return i(t)+"/s"}function p(t){if(t===0)return"--";const e=Math.floor(t/3600),a=Math.floor(t%3600/60),r=t%60;return e>0?`${e}:${a.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${a}:${r.toString().padStart(2,"0")}`}function l(t){const e={total:t.length,active:0,waiting:0,paused:0,complete:0,error:0,totalSize:0,completedSize:0,totalSpeed:0};return t.forEach(a=>{switch(a.status){case"active":e.active++;break;case"waiting":e.waiting++;break;case"paused":e.paused++;break;case"complete":e.complete++;break;case"error":e.error++;break}e.totalSize+=o(a),e.completedSize+=parseInt(a.completedLength),e.totalSpeed+=n(a)}),e}export{c as a,p as b,i as c,s as f,l as g};
