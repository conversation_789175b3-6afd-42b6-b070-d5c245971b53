import{d as N,x as O,r as S,c as R,a as j,L as q,E as u,g as F,l as a,u as p,f as V,v as G,y as L,k as o,e as r,B as $,o as y,m as n,P as H,p as w,A as J,i as K}from"./index-a49529e1.js";const Q={class:"aria2-basic-settings"},X=N({__name:"Aria2BasicSettings",setup(Y){const i=O(),f=S(),g=S(!1),x=S(!1),M=R(()=>!!window.electronAPI),t=j({dir:"",maxConcurrentDownloads:5,maxConnectionPerServer:5,minSplitSize:"10M",continue:!0,maxTries:5,retryWait:0,saveSession:!0,saveSessionInterval:60}),I={maxConcurrentDownloads:[{required:!0,message:"请输入最大同时下载数",trigger:"blur"},{type:"number",min:1,max:16,message:"值必须在1-16之间",trigger:"blur"}],maxConnectionPerServer:[{required:!0,message:"请输入每服务器最大连接数",trigger:"blur"},{type:"number",min:1,max:16,message:"值必须在1-16之间",trigger:"blur"}],maxTries:[{type:"number",min:0,max:100,message:"值必须在0-100之间",trigger:"blur"}],retryWait:[{type:"number",min:0,max:600,message:"值必须在0-600之间",trigger:"blur"}],saveSessionInterval:[{type:"number",min:60,max:3600,message:"值必须在60-3600之间",trigger:"blur"}]};q(()=>{i.isConnected&&_()});async function _(){if(!i.isConnected){u.warning("请先连接到 Aria2 服务器");return}g.value=!0;try{console.log("Loading Aria2 global settings...");const s=await i.getGlobalOptions();if(console.log("Received options:",s),s&&typeof s=="object")t.dir=s.dir||"",t.maxConcurrentDownloads=parseInt(s["max-concurrent-downloads"]||"5"),t.maxConnectionPerServer=parseInt(s["max-connection-per-server"]||"5"),t.minSplitSize=s["min-split-size"]||"10M",t.continue=s.continue==="true"||s.continue===!0,t.maxTries=parseInt(s["max-tries"]||"5"),t.retryWait=parseInt(s["retry-wait"]||"0"),t.saveSession=s["save-session"]!=="false"&&s["save-session"]!==!1,t.saveSessionInterval=parseInt(s["save-session-interval"]||"60"),console.log("Parsed settings:",t),u.success("设置加载成功");else throw new Error("Invalid options format received")}catch(s){const e=s instanceof Error?s.message:"未知错误";u.error(`加载设置失败: ${e}`),console.error("Failed to load settings:",s)}finally{g.value=!1}}async function D(){if(f.value){try{await f.value.validate()}catch{return}if(!i.isConnected){u.warning("请先连接到 Aria2 服务器");return}x.value=!0;try{const s={dir:t.dir,"max-concurrent-downloads":t.maxConcurrentDownloads.toString(),"max-connection-per-server":t.maxConnectionPerServer.toString(),"min-split-size":t.minSplitSize,continue:t.continue.toString(),"max-tries":t.maxTries.toString(),"retry-wait":t.retryWait.toString(),"save-session":t.saveSession.toString(),"save-session-interval":t.saveSessionInterval.toString()};await i.changeGlobalOptions(s),u.success("设置已保存")}catch(s){u.error("保存设置失败"),console.error("Failed to save settings:",s)}finally{x.value=!1}}}async function A(){try{await J.confirm("确定要恢复为默认设置吗？","确认恢复",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),Object.assign(t,{dir:"",maxConcurrentDownloads:5,maxConnectionPerServer:5,minSplitSize:"10M",continue:!0,maxTries:5,retryWait:0,saveSession:!0,saveSessionInterval:60}),u.success("已恢复为默认设置")}catch{}}async function P(){if(!window.electronAPI){u.warning("此功能仅在桌面版中可用");return}try{const s=await window.electronAPI.showOpenDialog({properties:["openDirectory"],title:"选择下载目录"});!s.canceled&&s.filePaths.length>0&&(t.dir=s.filePaths[0])}catch{u.error("选择目录失败")}}return(s,e)=>{const T=r("el-alert"),B=r("el-icon"),v=r("el-button"),k=r("el-input"),d=r("el-form-item"),c=r("el-input-number"),m=r("el-option"),z=r("el-select"),b=r("el-card"),C=r("el-switch"),U=r("el-space"),W=r("el-form"),E=$("loading");return y(),F("div",Q,[e[26]||(e[26]=a("div",{class:"settings-header"},[a("h2",null,"Aria2 基本设置"),a("p",{class:"settings-description"},"配置 Aria2 的基本下载参数")],-1)),p(i).isConnected?G("",!0):(y(),V(T,{key:0,title:"未连接到 Aria2 服务器",description:"请先连接到 Aria2 服务器才能修改设置",type:"warning",closable:!1,style:{"margin-bottom":"20px"}})),L((y(),V(W,{ref_key:"formRef",ref:f,model:t,rules:I,"label-width":"200px",style:{"max-width":"800px"},disabled:!p(i).isConnected},{default:o(()=>[n(b,{class:"setting-group"},{header:o(()=>[...e[9]||(e[9]=[a("span",{class:"group-title"},"下载设置",-1)])]),default:o(()=>[n(d,{label:"下载目录",prop:"dir"},{default:o(()=>[n(k,{modelValue:t.dir,"onUpdate:modelValue":e[0]||(e[0]=l=>t.dir=l),placeholder:"默认下载目录，如：D:\\Downloads"},{append:o(()=>[n(v,{onClick:P,disabled:!M.value},{default:o(()=>[n(B,null,{default:o(()=>[n(p(H))]),_:1})]),_:1},8,["disabled"])]),_:1},8,["modelValue"]),e[10]||(e[10]=a("div",{class:"form-tip"},"设置新任务的默认保存目录",-1))]),_:1}),n(d,{label:"最大同时下载数",prop:"maxConcurrentDownloads"},{default:o(()=>[n(c,{modelValue:t.maxConcurrentDownloads,"onUpdate:modelValue":e[1]||(e[1]=l=>t.maxConcurrentDownloads=l),min:1,max:16,style:{width:"200px"}},null,8,["modelValue"]),e[11]||(e[11]=a("div",{class:"form-tip"},"同时进行的下载任务数量限制（1-16）",-1))]),_:1}),n(d,{label:"每服务器最大连接数",prop:"maxConnectionPerServer"},{default:o(()=>[n(c,{modelValue:t.maxConnectionPerServer,"onUpdate:modelValue":e[2]||(e[2]=l=>t.maxConnectionPerServer=l),min:1,max:16,style:{width:"200px"}},null,8,["modelValue"]),e[12]||(e[12]=a("div",{class:"form-tip"},"对单个服务器的最大连接数（1-16）",-1))]),_:1}),n(d,{label:"最小分片大小",prop:"minSplitSize"},{default:o(()=>[n(z,{modelValue:t.minSplitSize,"onUpdate:modelValue":e[3]||(e[3]=l=>t.minSplitSize=l),style:{width:"200px"}},{default:o(()=>[n(m,{label:"1M",value:"1M"}),n(m,{label:"5M",value:"5M"}),n(m,{label:"10M",value:"10M"}),n(m,{label:"20M",value:"20M"}),n(m,{label:"50M",value:"50M"}),n(m,{label:"100M",value:"100M"})]),_:1},8,["modelValue"]),e[13]||(e[13]=a("div",{class:"form-tip"},"文件分片下载的最小大小",-1))]),_:1})]),_:1}),n(b,{class:"setting-group"},{header:o(()=>[...e[14]||(e[14]=[a("span",{class:"group-title"},"重试设置",-1)])]),default:o(()=>[n(d,{label:"断点续传",prop:"continue"},{default:o(()=>[n(C,{modelValue:t.continue,"onUpdate:modelValue":e[4]||(e[4]=l=>t.continue=l)},null,8,["modelValue"]),e[15]||(e[15]=a("div",{class:"form-tip"},"支持断点续传的下载任务",-1))]),_:1}),n(d,{label:"最大重试次数",prop:"maxTries"},{default:o(()=>[n(c,{modelValue:t.maxTries,"onUpdate:modelValue":e[5]||(e[5]=l=>t.maxTries=l),min:0,max:100,style:{width:"200px"}},null,8,["modelValue"]),e[16]||(e[16]=a("div",{class:"form-tip"},"下载失败时的最大重试次数（0表示不重试）",-1))]),_:1}),n(d,{label:"重试等待时间",prop:"retryWait"},{default:o(()=>[n(c,{modelValue:t.retryWait,"onUpdate:modelValue":e[6]||(e[6]=l=>t.retryWait=l),min:0,max:600,style:{width:"200px"}},null,8,["modelValue"]),e[17]||(e[17]=a("span",{style:{"margin-left":"8px"}},"秒",-1)),e[18]||(e[18]=a("div",{class:"form-tip"},"重试前的等待时间（0-600秒）",-1))]),_:1})]),_:1}),n(b,{class:"setting-group"},{header:o(()=>[...e[19]||(e[19]=[a("span",{class:"group-title"},"其他设置",-1)])]),default:o(()=>[n(d,{label:"自动保存会话",prop:"saveSession"},{default:o(()=>[n(C,{modelValue:t.saveSession,"onUpdate:modelValue":e[7]||(e[7]=l=>t.saveSession=l)},null,8,["modelValue"]),e[20]||(e[20]=a("div",{class:"form-tip"},"定期保存下载会话，重启后可恢复未完成的任务",-1))]),_:1}),n(d,{label:"会话保存间隔",prop:"saveSessionInterval"},{default:o(()=>[n(c,{modelValue:t.saveSessionInterval,"onUpdate:modelValue":e[8]||(e[8]=l=>t.saveSessionInterval=l),min:60,max:3600,disabled:!t.saveSession,style:{width:"200px"}},null,8,["modelValue","disabled"]),e[21]||(e[21]=a("span",{style:{"margin-left":"8px"}},"秒",-1)),e[22]||(e[22]=a("div",{class:"form-tip"},"自动保存会话的时间间隔（60-3600秒）",-1))]),_:1})]),_:1}),n(d,{style:{"margin-top":"24px"}},{default:o(()=>[n(U,null,{default:o(()=>[n(v,{type:"primary",onClick:D,loading:x.value,disabled:!p(i).isConnected},{default:o(()=>[...e[23]||(e[23]=[w(" 保存设置 ",-1)])]),_:1},8,["loading","disabled"]),n(v,{onClick:_,disabled:!p(i).isConnected},{default:o(()=>[...e[24]||(e[24]=[w(" 重新加载 ",-1)])]),_:1},8,["disabled"]),n(v,{onClick:A,disabled:!p(i).isConnected},{default:o(()=>[...e[25]||(e[25]=[w(" 恢复默认 ",-1)])]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1},8,["model","disabled"])),[[E,g.value]])])}}});const h=K(X,[["__scopeId","data-v-0a0c9dbf"]]);export{h as default};
