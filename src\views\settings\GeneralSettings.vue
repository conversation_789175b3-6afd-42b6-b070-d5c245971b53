<template>
  <div class="general-settings">
    <div class="settings-header">
      <h2>{{ $t('settings.general.title') }}</h2>
      <p class="settings-description">{{ $t('settings.general.description') }}</p>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      label-width="150px"
      style="max-width: 600px"
      v-loading="settingsStore.isLoading"
    >
      <el-form-item :label="$t('settings.general.language')">
        <el-select v-model="form.language" style="width: 100%" @change="handleLanguageChange">
          <el-option label="简体中文" value="zh-CN" />
          <el-option label="繁体中文" value="zh-TW" />
          <el-option label="English" value="en" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('settings.general.theme')">
        <el-select v-model="form.theme" style="width: 100%" @change="handleThemeChange">
          <el-option :label="$t('settings.general.lightTheme')" value="light" />
          <el-option :label="$t('settings.general.darkTheme')" value="dark" />
          <el-option :label="$t('settings.general.autoTheme')" value="auto" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('settings.general.refreshInterval')">
        <el-select v-model="form.refreshInterval" style="width: 100%">
          <el-option label="1秒" :value="1000" />
          <el-option label="2秒" :value="2000" />
          <el-option label="5秒" :value="5000" />
          <el-option label="10秒" :value="10000" />
        </el-select>
        <div class="form-tip">{{ $t('settings.general.refreshIntervalTip') }}</div>
      </el-form-item>

      <el-form-item :label="$t('settings.general.autoConnect')">
        <el-switch v-model="form.autoConnect" />
        <div class="form-tip">{{ $t('settings.general.autoConnectTip') }}</div>
      </el-form-item>

      <el-form-item :label="$t('settings.general.minimizeToTray')">
        <el-switch v-model="form.minimizeToTray" />
        <div class="form-tip">{{ $t('settings.general.minimizeToTrayTip') }}</div>
      </el-form-item>

      <el-divider />

      <el-form-item :label="$t('settings.general.uiSettings')">
        <el-space direction="vertical" style="width: 100%">
          <el-checkbox v-model="form.ui.showStatusBar">{{ $t('settings.general.showStatusBar') }}</el-checkbox>
          <el-checkbox v-model="form.ui.showToolbar">{{ $t('settings.general.showToolbar') }}</el-checkbox>
        </el-space>
      </el-form-item>

      <el-form-item :label="$t('settings.general.defaultView')">
        <el-radio-group v-model="form.ui.defaultView">
          <el-radio label="downloading">{{ $t('taskStatus.active') }}</el-radio>
          <el-radio label="waiting">{{ $t('taskStatus.waiting') }}</el-radio>
          <el-radio label="stopped">{{ $t('taskStatus.complete') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-divider />

      <el-form-item>
        <el-space>
          <el-button type="primary" @click="saveSettings" :loading="saving">
            {{ $t('settings.general.saveSettings') }}
          </el-button>
          <el-button @click="resetSettings" :disabled="settingsStore.isLoading">
            {{ $t('settings.general.resetToDefault') }}
          </el-button>
          <el-button @click="exportSettings">
            {{ $t('settings.general.exportSettings') }}
          </el-button>
          <el-button @click="showImportDialog = true">
            {{ $t('settings.general.importSettings') }}
          </el-button>
        </el-space>
      </el-form-item>
    </el-form>

    <!-- 导入设置对话框 -->
    <el-dialog
      v-model="showImportDialog"
      :title="$t('dialogs.importSettings.title')"
      width="500px"
    >
      <el-form>
        <el-form-item label="设置文件">
          <el-input
            v-model="importText"
            type="textarea"
            :rows="10"
            :placeholder="$t('dialogs.importSettings.placeholder')"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showImportDialog = false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="importSettings" :loading="importing">
          {{ $t('dialogs.importSettings.import') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { useSettingsStore } from '@/stores/settingsStore'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const settingsStore = useSettingsStore()
const formRef = ref<FormInstance>()
const saving = ref(false)
const importing = ref(false)
const showImportDialog = ref(false)
const importText = ref('')
const isInitialized = ref(false)
const autoSaveTimer = ref<NodeJS.Timeout | null>(null)

// 表单数据
const form = reactive({
  language: 'zh-CN',
  theme: 'light' as 'light' | 'dark' | 'auto',
  refreshInterval: 1000,
  autoConnect: true,
  minimizeToTray: true,
  ui: {
    showStatusBar: true,
    showToolbar: true,
    defaultView: 'downloading' as 'downloading' | 'waiting' | 'stopped'
  }
})

// 监听设置变化
watch(() => settingsStore.settings, (newSettings) => {
  Object.assign(form, {
    language: newSettings.language,
    theme: newSettings.theme,
    refreshInterval: newSettings.refreshInterval,
    autoConnect: newSettings.autoConnect,
    minimizeToTray: newSettings.minimizeToTray,
    ui: { ...newSettings.ui }
  })

  // 标记为已初始化
  nextTick(() => {
    isInitialized.value = true
  })
}, { immediate: true, deep: true })

// 监听表单变化，自动保存
watch(form, () => {
  if (!isInitialized.value) return

  // 清除之前的定时器
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value)
  }

  // 设置新的定时器，延迟500ms保存（防抖）
  autoSaveTimer.value = setTimeout(() => {
    autoSaveSettings()
  }, 500)
}, { deep: true })

onMounted(async () => {
  await settingsStore.initialize()
})

// 自动保存设置（静默保存，不显示提示）
async function autoSaveSettings() {
  if (saving.value) return // 如果正在保存，跳过

  saving.value = true
  try {
    await settingsStore.updateAriaNgSettings(form)
    await settingsStore.updateUIConfig(form.ui)
    // 自动保存不显示成功提示，保持界面简洁
  } catch (error) {
    // 自动保存失败时显示错误提示
    ElMessage.error(t('settings.general.autoSaveFailed') || '自动保存失败')
  } finally {
    saving.value = false
  }
}

// 手动保存设置（显示成功提示）
async function saveSettings() {
  saving.value = true
  try {
    await settingsStore.updateAriaNgSettings(form)
    await settingsStore.updateUIConfig(form.ui)
    ElMessage.success(t('settings.general.settingsSaved'))
  } catch (error) {
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

async function resetSettings() {
  try {
    await ElMessageBox.confirm(
      t('dialogs.deleteTask.confirmMessage'),
      t('dialogs.deleteTask.confirmDelete'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )

    await settingsStore.resetSettings()
    ElMessage.success(t('settings.general.settingsReset'))
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置设置失败')
    }
  }
}

function exportSettings() {
  try {
    const settingsJson = settingsStore.exportSettings()

    // 创建下载链接
    const blob = new Blob([settingsJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `aria2-desktop-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(t('settings.general.settingsExported'))
  } catch (error) {
    ElMessage.error('导出设置失败')
  }
}

async function importSettings() {
  if (!importText.value.trim()) {
    ElMessage.warning(t('dialogs.importSettings.inputRequired'))
    return
  }

  importing.value = true
  try {
    await settingsStore.importSettings(importText.value)
    showImportDialog.value = false
    importText.value = ''
    ElMessage.success(t('settings.general.settingsImported'))
  } catch (error) {
    ElMessage.error(t('dialogs.importSettings.importFailed', { error: error instanceof Error ? error.message : '未知错误' }))
  } finally {
    importing.value = false
  }
}

function handleThemeChange() {
  // 立即应用主题变更，使用当前表单的主题值
  applyThemeImmediately(form.theme)
}

// 立即应用主题的函数
function applyThemeImmediately(theme: string) {
  const isDark = theme === 'dark' ||
    (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)

  document.documentElement.classList.toggle('dark', isDark)
}

function handleLanguageChange() {
  // 语言变更会通过watch自动保存，这里只显示提示
  ElMessage.info(t('settings.general.languageChangeNotice'))
}
</script>

<style scoped>
.general-settings {
  padding: 20px;
}

.settings-header {
  margin-bottom: 24px;
}

.settings-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.settings-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-divider) {
  margin: 24px 0;
}
</style>
