import{d as we,x as ke,r as S,c as X,L as Ie,T as xe,E as y,g as p,l as c,m as a,k as l,X as Se,e as _,o as d,p as u,q as r,u as M,Y,f as b,v as w,Z as Te,$ as Pe,Q as V,R,n as Fe,i as Le}from"./index-a49529e1.js";import{f as Ue,a as Ce,b as $e}from"./taskUtils-6207ebc5.js";const De={class:"task-detail"},ze={class:"task-detail-header"},Be={key:0,class:"task-detail-content"},Me={class:"info-section"},Ve={class:"field-with-action"},Re={class:"path-with-action"},Ee={class:"field-with-action"},Ne={class:"uri-content-full"},Ae={class:"uri-text-full"},Ge={key:0,class:"file-actions"},qe={key:0},Ke={key:0},Oe={key:1},je={key:0},Qe={class:"pieces-info"},Xe={class:"pieces-visual",style:{"margin-top":"20px"}},Ye={class:"pieces-grid"},Ze=["title"],He={key:1,class:"loading"},Je=we({__name:"TaskDetail",props:{gid:{}},setup(Z){const H=Z,J=Se(),h=ke(),n=S(null),A=S(!1),G=S(!1),W=S([]),P=S([]),F=S([]),C=S([]),q=S("basic"),k=X(()=>H.gid||J.params.gid),K=X(()=>!!window.electronAPI);Ie(async()=>{await O();const t=setInterval(async()=>{h.isConnected&&k.value&&n.value&&["active","waiting","paused"].includes(n.value.status)&&await O()},3e3);xe(()=>{clearInterval(t)})});async function O(){if(!(!h.isConnected||!k.value)){A.value=!0;try{let t=ee(k.value);if(!t&&h.service&&(t=await h.service.tellStatus(k.value,["gid","status","totalLength","completedLength","uploadLength","downloadSpeed","uploadSpeed","connections","numPieces","pieceLength","dir","files","bittorrent","errorCode","errorMessage"])),t&&(n.value=t,h.service))try{const e=await h.service.getFiles(k.value);e&&n.value&&(n.value.files=e);try{const i=await h.service.getUris(k.value);console.log("原始 URI 数据:",i),P.value=Q(i||[])}catch(i){if(console.warn("Failed to get URIs (task may be completed):",i),n.value.files&&n.value.files.length>0){const f=[];n.value.files.forEach((o,v)=>{o.uris&&o.uris.length>0&&o.uris.forEach(I=>{f.push({...I,fileIndex:v,fileName:j(o.path)})})}),P.value=Q(f)}}if(n.value.bittorrent)try{const i=await h.service.getPeers(k.value);F.value=i||[]}catch(i){console.warn("Failed to get peers:",i),F.value=[]}try{const i=await h.service.getServers(k.value);C.value=i||[]}catch(i){console.warn("Failed to get servers:",i),C.value=[]}}catch(e){console.warn("Failed to get task details:",e)}}catch(t){console.error("Failed to load task detail:",t),y.error("加载任务详情失败")}finally{A.value=!1}}}function ee(t){return[...h.activeTasks,...h.waitingTasks,...h.stoppedTasks].find(i=>i.gid===t)||null}function $(t){const e=parseInt(t);if(e===0)return"0 B";const i=1024,f=["B","KB","MB","GB","TB"],o=Math.floor(Math.log(e)/Math.log(i));return parseFloat((e/Math.pow(i,o)).toFixed(1))+" "+f[o]}function D(t){const e=parseInt(t)||0;return Ue(e)}function te(t){const e=Ce(t);return $e(e)}function le(t){switch(t){case"active":return"primary";case"waiting":return"warning";case"paused":return"info";case"complete":return"success";case"error":return"danger";default:return""}}function ae(t){switch(t){case"active":return"下载中";case"waiting":return"等待中";case"paused":return"已暂停";case"complete":return"已完成";case"error":return"错误";case"removed":return"已删除";default:return t}}function ne(t){const e=parseInt(t.totalLength),i=parseInt(t.completedLength);return e===0?0:Math.round(i/e*100)}function j(t){return t.split("/").pop()||t.split("\\").pop()||t}function Q(t){if(!t||t.length===0)return[];const e=new Set,i=[];return t.forEach(f=>{let o;if(typeof f=="string")o=f;else if(f&&f.uri)o=f.uri;else return;e.has(o)||(e.add(o),i.push(f))}),t.length!==i.length&&console.log(`URI 去重: ${t.length} -> ${i.length} (移除了 ${t.length-i.length} 个重复项)`),i}function se(t){return!t||t==="0"?"未知":new Date(parseInt(t)*1e3).toLocaleString()}function oe(){var t;(t=n.value)!=null&&t.gid&&(navigator.clipboard.writeText(n.value.gid),y.success("GID 已复制到剪贴板"))}function ie(t){navigator.clipboard.writeText(t),y.success("下载链接已复制到剪贴板")}async function re(t){if(!window.electronAPI){y.warning("此功能仅在桌面版中可用");return}try{console.log("Opening file in folder:",t);const e=await window.electronAPI.showItemInFolder(t);e!=null&&e.success?y.success("已打开文件位置"):y.error(`打开文件位置失败: ${(e==null?void 0:e.error)||"未知错误"}`)}catch(e){console.error("Failed to open file in folder:",e),y.error("打开文件位置失败")}}async function ue(t){if(!window.electronAPI){y.warning("此功能仅在桌面版中可用");return}try{console.log("Opening file:",t);const e=await window.electronAPI.openPath(t);e!=null&&e.success?y.success("已打开文件"):y.error(`打开文件失败: ${(e==null?void 0:e.error)||"未知错误"}`)}catch(e){console.error("Failed to open file:",e),y.error("打开文件失败")}}function de(t){switch(t){case"used":return"success";case"waiting":return"warning";default:return"info"}}function ce(t){switch(t){case"used":return"使用中";case"waiting":return"等待中";default:return t||"未知"}}function fe(t){switch(t){case"used":return"success";case"waiting":return"warning";default:return"info"}}function pe(t){switch(t){case"used":return"使用中";case"waiting":return"等待中";default:return t||"未知"}}function _e(t,e){return!t&&!e?"success":t&&e?"danger":"warning"}function ve(t,e){return!t&&!e?"正常":t&&e?"阻塞":t?"我方阻塞":e?"对方阻塞":"未知"}function ge(){var i;if(!((i=n.value)!=null&&i.bitfield))return"0";const t=n.value.bitfield;let e=0;for(let f=0;f<t.length;f++){const o=parseInt(t.substr(f*2,2),16);for(let v=0;v<8;v++)o&1<<7-v&&e++}return e.toString()}function me(){var o,v;if(!((o=n.value)!=null&&o.bitfield)||!((v=n.value)!=null&&v.numPieces))return[];const t=parseInt(n.value.numPieces),e=n.value.bitfield,i=[],f=Math.min(t,1e3);for(let I=0;I<f;I++){const T=Math.floor(I/8),L=I%8;if(T*2+1<e.length){const E=parseInt(e.substr(T*2,2),16);i.push(!!(E&1<<7-L))}else i.push(!1)}return i}return(t,e)=>{const i=_("el-button"),f=_("el-icon"),o=_("el-descriptions-item"),v=_("el-tag"),I=_("el-progress"),T=_("el-text"),L=_("el-descriptions"),E=_("el-space"),U=_("el-card"),z=_("el-tab-pane"),g=_("el-table-column"),N=_("el-table"),B=_("el-empty"),be=_("el-tabs"),he=_("el-dialog");return d(),p("div",De,[c("div",ze,[e[7]||(e[7]=c("h2",null,"任务详情",-1)),a(i,{onClick:e[0]||(e[0]=s=>t.$router.go(-1))},{default:l(()=>[...e[6]||(e[6]=[u("返回",-1)])]),_:1})]),n.value?(d(),p("div",Be,[a(be,{modelValue:q.value,"onUpdate:modelValue":e[4]||(e[4]=s=>q.value=s),class:"detail-tabs"},{default:l(()=>[a(z,{label:"基本信息",name:"basic"},{default:l(()=>[a(U,{class:"info-card"},{default:l(()=>{var s;return[c("div",Me,[a(L,{column:2,border:"",class:"task-descriptions"},{default:l(()=>{var m,x;return[a(o,{label:"GID"},{default:l(()=>[c("div",Ve,[c("span",null,r(n.value.gid),1),a(i,{size:"small",text:"",onClick:oe,title:"复制 GID"},{default:l(()=>[a(f,null,{default:l(()=>[a(M(Y))]),_:1})]),_:1})])]),_:1}),a(o,{label:"状态"},{default:l(()=>[a(v,{type:le(n.value.status)},{default:l(()=>[u(r(ae(n.value.status)),1)]),_:1},8,["type"])]),_:1}),(m=n.value.files)!=null&&m.length?(d(),b(o,{key:0,label:"文件名"},{default:l(()=>[c("span",null,r(j(n.value.files[0].path)),1)]),_:1})):w("",!0),a(o,{label:"文件大小"},{default:l(()=>[u(r($(n.value.totalLength)),1)]),_:1}),a(o,{label:"已下载"},{default:l(()=>[u(r($(n.value.completedLength)),1)]),_:1}),a(o,{label:"下载进度"},{default:l(()=>[a(I,{percentage:ne(n.value),status:n.value.status==="complete"?"success":void 0,style:{width:"200px"}},null,8,["percentage","status"])]),_:1}),a(o,{label:"剩余时间"},{default:l(()=>[u(r(te(n.value)),1)]),_:1}),a(o,{label:"下载速度"},{default:l(()=>[u(r(D(n.value.downloadSpeed)),1)]),_:1}),a(o,{label:"分片数"},{default:l(()=>[u(r(n.value.numPieces||0),1)]),_:1}),a(o,{label:"分片长度"},{default:l(()=>[u(r($(n.value.pieceLength||"0")),1)]),_:1}),(x=n.value.files)!=null&&x.length?(d(),b(o,{key:1,label:"文件路径",span:2},{default:l(()=>[c("div",Re,[c("span",null,r(n.value.files[0].path),1),K.value?(d(),b(i,{key:0,size:"small",text:"",onClick:e[1]||(e[1]=ye=>re(n.value.files[0].path)),title:"打开位置",style:{"margin-left":"8px"}},{default:l(()=>[a(f,null,{default:l(()=>[a(M(Te))]),_:1})]),_:1})):w("",!0)])]),_:1})):w("",!0),P.value.length?(d(),b(o,{key:2,label:"下载链接",span:2},{default:l(()=>[c("div",Ee,[c("div",Ne,[c("span",Ae,r(P.value[0].uri),1)]),a(i,{size:"small",text:"",onClick:e[2]||(e[2]=ye=>ie(P.value[0].uri)),title:"复制链接"},{default:l(()=>[a(f,null,{default:l(()=>[a(M(Y))]),_:1})]),_:1})])]),_:1})):w("",!0),n.value.errorCode&&n.value.errorCode!=="0"?(d(),b(o,{key:3,label:"错误信息",span:2},{default:l(()=>[a(T,{type:"danger"},{default:l(()=>[u(r(n.value.errorMessage||n.value.errorCode),1)]),_:1})]),_:1})):w("",!0)]}),_:1}),(s=n.value.files)!=null&&s.length&&n.value.status==="complete"?(d(),p("div",Ge,[a(E,null,{default:l(()=>[K.value?(d(),b(i,{key:0,size:"small",onClick:e[3]||(e[3]=m=>ue(n.value.files[0].path))},{default:l(()=>[a(f,null,{default:l(()=>[a(M(Pe))]),_:1}),e[8]||(e[8]=u(" 打开文件 ",-1))]),_:1})):w("",!0)]),_:1})])):w("",!0)])]}),_:1}),n.value.bittorrent?(d(),b(U,{key:0,class:"info-card"},{header:l(()=>[...e[9]||(e[9]=[c("span",null,"BitTorrent 信息",-1)])]),default:l(()=>[a(L,{column:2,border:""},{default:l(()=>[a(o,{label:"种子名称"},{default:l(()=>{var s;return[u(r(((s=n.value.bittorrent.info)==null?void 0:s.name)||"未知"),1)]}),_:1}),a(o,{label:"创建者"},{default:l(()=>[u(r(n.value.bittorrent.createdBy||"未知"),1)]),_:1}),a(o,{label:"创建时间"},{default:l(()=>[u(r(se(n.value.bittorrent.creationDate)),1)]),_:1}),a(o,{label:"注释"},{default:l(()=>[u(r(n.value.bittorrent.comment||"无"),1)]),_:1}),a(o,{label:"模式"},{default:l(()=>[u(r(n.value.bittorrent.mode||"未知"),1)]),_:1}),a(o,{label:"宣布列表"},{default:l(()=>{var s;return[(s=n.value.bittorrent.announceList)!=null&&s.length?(d(),p("div",qe,[(d(!0),p(V,null,R(n.value.bittorrent.announceList.slice(0,3),(m,x)=>(d(),b(v,{key:x,size:"small",style:{margin:"2px"}},{default:l(()=>[u(r(m[0]),1)]),_:2},1024))),128)),n.value.bittorrent.announceList.length>3?(d(),p("span",Ke," 等 "+r(n.value.bittorrent.announceList.length)+" 个 ",1)):w("",!0)])):(d(),p("span",Oe,"无"))]}),_:1})]),_:1})]),_:1})):w("",!0)]),_:1}),a(z,{name:"servers"},{label:l(()=>[...e[10]||(e[10]=[c("span",null,"服务器信息",-1)])]),default:l(()=>[a(U,{class:"info-card"},{default:l(()=>[C.value.length?(d(),b(N,{key:0,data:C.value,style:{width:"100%"}},{default:l(()=>[a(g,{type:"index",label:"序号",width:"60"}),a(g,{label:"服务器","min-width":"300"},{default:l(({row:s})=>[(d(!0),p(V,null,R(s.servers,(m,x)=>(d(),p("div",{key:x,class:"server-item"},[a(T,{copyable:""},{default:l(()=>[u(r(m.uri),1)]),_:2},1024),a(v,{type:fe(m.status),size:"small",style:{"margin-left":"8px"}},{default:l(()=>[u(r(pe(m.status)),1)]),_:2},1032,["type"])]))),128))]),_:1}),a(g,{label:"下载速度",width:"120"},{default:l(({row:s})=>[(d(!0),p(V,null,R(s.servers,(m,x)=>(d(),p("div",{key:x,class:"server-item"},r(D(m.downloadSpeed||"0")),1))),128))]),_:1})]),_:1},8,["data"])):(d(),b(B,{key:1,description:"没有服务器信息"}))]),_:1})]),_:1}),n.value.bittorrent?(d(),b(z,{key:0,name:"peers"},{label:l(()=>[c("span",null,"Peer 信息 ("+r(F.value.length)+")",1)]),default:l(()=>[a(U,{class:"info-card"},{default:l(()=>[F.value.length?(d(),b(N,{key:0,data:F.value,style:{width:"100%"}},{default:l(()=>[a(g,{type:"index",label:"序号",width:"60"}),a(g,{label:"IP 地址",width:"150"},{default:l(({row:s})=>[u(r(s.ip),1)]),_:1}),a(g,{label:"端口",width:"80"},{default:l(({row:s})=>[u(r(s.port),1)]),_:1}),a(g,{label:"客户端",width:"200"},{default:l(({row:s})=>[u(r(s.peerId||"未知"),1)]),_:1}),a(g,{label:"下载速度",width:"120"},{default:l(({row:s})=>[u(r(D(s.downloadSpeed||"0")),1)]),_:1}),a(g,{label:"上传速度",width:"120"},{default:l(({row:s})=>[u(r(D(s.uploadSpeed||"0")),1)]),_:1}),a(g,{label:"进度",width:"100"},{default:l(({row:s})=>[u(r((parseFloat(s.bitfield||"0")*100).toFixed(1))+"% ",1)]),_:1}),a(g,{label:"状态",width:"100"},{default:l(({row:s})=>[a(v,{type:_e(s.amChoking,s.peerChoking),size:"small"},{default:l(()=>[u(r(ve(s.amChoking,s.peerChoking)),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])):(d(),b(B,{key:1,description:"没有 Peer 信息"}))]),_:1})]),_:1})):w("",!0),a(z,{name:"pieces"},{label:l(()=>[...e[11]||(e[11]=[c("span",null,"区块信息",-1)])]),default:l(()=>[a(U,{class:"info-card"},{default:l(()=>[n.value.numPieces&&parseInt(n.value.numPieces)>0?(d(),p("div",je,[c("div",Qe,[a(L,{column:3,border:""},{default:l(()=>[a(o,{label:"总区块数"},{default:l(()=>[u(r(n.value.numPieces),1)]),_:1}),a(o,{label:"区块大小"},{default:l(()=>[u(r($(n.value.pieceLength||"0")),1)]),_:1}),a(o,{label:"完成区块"},{default:l(()=>[u(r(ge()),1)]),_:1})]),_:1})]),c("div",Xe,[e[12]||(e[12]=c("h4",null,"区块完成状态",-1)),c("div",Ye,[(d(!0),p(V,null,R(me(),(s,m)=>(d(),p("div",{key:m,class:Fe(["piece-block",s?"completed":"pending"]),title:`区块 ${m}: ${s?"已完成":"未完成"}`},null,10,Ze))),128))]),e[13]||(e[13]=c("div",{class:"pieces-legend"},[c("span",{class:"legend-item"},[c("span",{class:"legend-color completed"}),u(" 已完成 ")]),c("span",{class:"legend-item"},[c("span",{class:"legend-color pending"}),u(" 未完成 ")])],-1))])])):(d(),b(B,{key:1,description:"没有区块信息"}))]),_:1})]),_:1})]),_:1},8,["modelValue"]),a(he,{modelValue:G.value,"onUpdate:modelValue":e[5]||(e[5]=s=>G.value=s),title:"文件 URI 列表",width:"70%"},{default:l(()=>[a(N,{data:W.value,style:{width:"100%"}},{default:l(()=>[a(g,{type:"index",label:"序号",width:"60"}),a(g,{prop:"uri",label:"URI","min-width":"400"},{default:l(({row:s})=>[a(T,{copyable:""},{default:l(()=>[u(r(s.uri),1)]),_:2},1024)]),_:1}),a(g,{prop:"status",label:"状态",width:"100"},{default:l(({row:s})=>[a(v,{type:de(s.status),size:"small"},{default:l(()=>[u(r(ce(s.status)),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])]),_:1},8,["modelValue"])])):(d(),p("div",He,[a(B,{description:"任务不存在或加载中..."})]))])}}});const tt=Le(Je,[["__scopeId","data-v-70b1f3ec"]]);export{tt as default};
