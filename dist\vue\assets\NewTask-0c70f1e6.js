import{d as G,x as L,K,r as b,a as M,L as j,g as J,l as x,m as t,k as l,z as O,e as a,o as Q,p as g,u as W,M as X,E as d,i as Y}from"./index-a49529e1.js";const Z={class:"new-task"},ee=G({__name:"NewTask",setup(te){const F=O(),p=L(),T=K(),V=b("uri"),m=b(!1),S=b(),v=b(),r=M({uris:"",dir:"",out:"",maxConnectionPerServer:5,minSplitSize:"10M",autoStart:!0}),s=M({torrentFile:null,dir:"",autoStart:!0}),P={uris:[{required:!0,message:"请输入下载链接",trigger:"blur"}]},R={torrentFile:[{required:!0,message:"请选择种子文件",trigger:"change"}]};async function U(){if(S.value)try{if(await S.value.validate(),!p.isConnected){d.error("请先连接到 Aria2 服务器");return}console.log("Aria2 connection status:",{isConnected:p.isConnected,service:!!p.service}),m.value=!0;const o=r.uris.split(`
`).map(n=>n.trim()).filter(n=>n.length>0);if(o.length===0){d.error("请输入有效的下载链接");return}const e={};r.dir&&r.dir.trim()&&(e.dir=r.dir.trim()),r.out&&r.out.trim()&&(e.out=r.out.trim()),e["max-connection-per-server"]=r.maxConnectionPerServer.toString(),e["min-split-size"]=r.minSplitSize,r.autoStart||(e.pause="true"),console.log("URI download options:",e);const u=await p.addUri(o,e);console.log("Task added with GID:",u),d.success(`已添加 ${o.length} 个下载任务`),F.push("/downloading")}catch(o){console.error("Failed to add URI task:",o);const e=o instanceof Error?o.message:"添加任务失败";d.error(`添加任务失败: ${e}`)}finally{m.value=!1}}function z(o){o.raw&&(s.torrentFile=o.raw)}async function h(){if(v.value)try{if(await v.value.validate(),!p.isConnected){d.error("请先连接到 Aria2 服务器");return}if(!s.torrentFile){d.error("请选择种子文件");return}m.value=!0;const o=await N(s.torrentFile),e={};s.dir&&(e.dir=s.dir),s.autoStart||(e.pause="true");const u=await p.addTorrent(o,[],e);console.log("Torrent task added with GID:",u),d.success("种子任务已添加"),F.push("/downloading")}catch(o){console.error("Failed to add torrent task:",o);const e=o instanceof Error?o.message:"添加种子任务失败";d.error(`添加种子任务失败: ${e}`)}finally{m.value=!1}}function A(){var o;(o=S.value)==null||o.resetFields()}function D(){var o;(o=v.value)==null||o.resetFields(),s.torrentFile=null}function N(o){return new Promise((e,u)=>{const n=new FileReader;n.onload=()=>{const w=n.result.split(",")[1];e(w)},n.onerror=u,n.readAsDataURL(o)})}return j(async()=>{await T.initialize();const o=T.downloadConfig;r.dir=o.defaultDir,r.maxConnectionPerServer=o.maxConnectionPerServer,r.minSplitSize=o.minSplitSize,r.autoStart=o.autoStart,s.dir=o.defaultDir,s.autoStart=o.autoStart}),(o,e)=>{const u=a("el-input"),n=a("el-form-item"),c=a("el-col"),w=a("el-row"),B=a("el-input-number"),f=a("el-option"),E=a("el-select"),I=a("el-switch"),_=a("el-button"),y=a("el-space"),C=a("el-form"),k=a("el-tab-pane"),H=a("el-icon"),$=a("el-upload"),q=a("el-tabs");return Q(),J("div",Z,[e[13]||(e[13]=x("div",{class:"new-task-header"},[x("h2",null,"新建下载任务"),x("p",{class:"header-description"},"支持 HTTP/HTTPS、FTP/SFTP、BitTorrent、Metalink 等多种协议")],-1)),t(q,{modelValue:V.value,"onUpdate:modelValue":e[6]||(e[6]=i=>V.value=i),class:"task-tabs"},{default:l(()=>[t(k,{label:"链接下载",name:"uri"},{default:l(()=>[t(C,{ref_key:"uriFormRef",ref:S,model:r,rules:P,"label-width":"120px",style:{"max-width":"800px"}},{default:l(()=>[t(n,{label:"下载链接",prop:"uris"},{default:l(()=>[t(u,{modelValue:r.uris,"onUpdate:modelValue":e[0]||(e[0]=i=>r.uris=i),type:"textarea",rows:6,placeholder:`请输入下载链接，每行一个
支持协议：HTTP/HTTPS、FTP/SFTP、磁力链接等

示例：
https://example.com/file.zip
magnet:?xt=urn:btih:...`},null,8,["modelValue"])]),_:1}),t(w,{gutter:20},{default:l(()=>[t(c,{span:12},{default:l(()=>[t(n,{label:"保存目录",prop:"dir"},{default:l(()=>[t(u,{modelValue:r.dir,"onUpdate:modelValue":e[1]||(e[1]=i=>r.dir=i),placeholder:"留空使用默认目录"},null,8,["modelValue"])]),_:1})]),_:1}),t(c,{span:12},{default:l(()=>[t(n,{label:"文件名",prop:"out"},{default:l(()=>[t(u,{modelValue:r.out,"onUpdate:modelValue":e[2]||(e[2]=i=>r.out=i),placeholder:"可选，指定文件名"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(w,{gutter:20},{default:l(()=>[t(c,{span:8},{default:l(()=>[t(n,{label:"最大连接数"},{default:l(()=>[t(B,{modelValue:r.maxConnectionPerServer,"onUpdate:modelValue":e[3]||(e[3]=i=>r.maxConnectionPerServer=i),min:1,max:16,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),t(c,{span:8},{default:l(()=>[t(n,{label:"分片大小"},{default:l(()=>[t(E,{modelValue:r.minSplitSize,"onUpdate:modelValue":e[4]||(e[4]=i=>r.minSplitSize=i),style:{width:"100%"}},{default:l(()=>[t(f,{label:"1M",value:"1M"}),t(f,{label:"5M",value:"5M"}),t(f,{label:"10M",value:"10M"}),t(f,{label:"20M",value:"20M"}),t(f,{label:"50M",value:"50M"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(c,{span:8},{default:l(()=>[t(n,{label:"自动开始"},{default:l(()=>[t(I,{modelValue:r.autoStart,"onUpdate:modelValue":e[5]||(e[5]=i=>r.autoStart=i)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(n,null,{default:l(()=>[t(y,null,{default:l(()=>[t(_,{type:"primary",onClick:U,loading:m.value},{default:l(()=>[...e[7]||(e[7]=[g(" 开始下载 ",-1)])]),_:1},8,["loading"]),t(_,{onClick:A},{default:l(()=>[...e[8]||(e[8]=[g("重置",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),t(k,{label:"种子文件",name:"torrent"},{default:l(()=>[t(C,{ref_key:"torrentFormRef",ref:v,model:s,rules:R,"label-width":"120px",style:{"max-width":"800px"}},{default:l(()=>[t(n,{label:"种子文件",prop:"torrentFile"},{default:l(()=>[t($,{ref:"uploadRef","auto-upload":!1,"show-file-list":!0,limit:1,accept:".torrent",onChange:z},{tip:l(()=>[...e[10]||(e[10]=[x("div",{class:"el-upload__tip"}," 只能上传 .torrent 文件 ",-1)])]),default:l(()=>[t(_,{type:"primary"},{default:l(()=>[t(H,null,{default:l(()=>[t(W(X))]),_:1}),e[9]||(e[9]=g(" 选择种子文件 ",-1))]),_:1})]),_:1},512)]),_:1}),t(n,null,{default:l(()=>[t(y,null,{default:l(()=>[t(_,{type:"primary",onClick:h,loading:m.value},{default:l(()=>[...e[11]||(e[11]=[g(" 开始下载 ",-1)])]),_:1},8,["loading"]),t(_,{onClick:D},{default:l(()=>[...e[12]||(e[12]=[g("重置",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"])])}}});const le=Y(ee,[["__scopeId","data-v-60aa4a1c"]]);export{le as default};
