import{d as h,x as I,r as w,a as P,L as B,E as m,g as F,l as i,y as H,f as M,k as o,e as u,B as E,o as y,m as a,u as L,p as V,i as N}from"./index-a49529e1.js";const q={class:"advanced-settings"},G=h({__name:"Aria2AdvancedSettings",setup(K){const d=I(),A=w(),c=w(!1),k=w(!1),e=P({diskCache:16,fileAllocation:"prealloc",checkIntegrity:!1,realtimeChunkChecksum:!0,logLevel:"warn",log:"",consoleLogLevel:"notice",maxOverallDownloadLimit:"0",maxOverallUploadLimit:"0",maxDownloadResult:1e3,uriSelector:"feedback",rpcSecret:"",allowOverwrite:!1,autoFileRenaming:!0,checksumAlgorithm:"sha-1",eventPoll:"epoll",checksumCheck:!0});B(()=>{d.isConnected&&x()});async function x(){if(!d.isConnected){m.warning("请先连接到 Aria2 服务器");return}c.value=!0;try{const n=await d.getGlobalOptions();n&&(e.diskCache=parseInt(n["disk-cache"]||"16"),e.fileAllocation=n["file-allocation"]||"prealloc",e.checkIntegrity=n["check-integrity"]==="true",e.realtimeChunkChecksum=n["realtime-chunk-checksum"]!=="false",e.logLevel=n["log-level"]||"warn",e.log=n.log||"",e.consoleLogLevel=n["console-log-level"]||"notice",e.maxOverallDownloadLimit=n["max-overall-download-limit"]||"0",e.maxOverallUploadLimit=n["max-overall-upload-limit"]||"0",e.maxDownloadResult=parseInt(n["max-download-result"]||"1000"),e.uriSelector=n["uri-selector"]||"feedback",e.rpcSecret=n["rpc-secret"]||"",e.allowOverwrite=n["allow-overwrite"]==="true",e.autoFileRenaming=n["auto-file-renaming"]!=="false",e.checksumAlgorithm=n.checksum||"sha-1",e.eventPoll=n["event-poll"]||"epoll",e.checksumCheck=n["checksum-check"]!=="false"),m.success("设置加载成功")}catch(n){m.error("加载设置失败"),console.error("Failed to load advanced settings:",n)}finally{c.value=!1}}async function S(){if(!d.isConnected){m.warning("请先连接到 Aria2 服务器");return}k.value=!0;try{const n={"disk-cache":e.diskCache.toString()+"M","file-allocation":e.fileAllocation,"check-integrity":e.checkIntegrity?"true":"false","realtime-chunk-checksum":e.realtimeChunkChecksum?"true":"false","log-level":e.logLevel,"console-log-level":e.consoleLogLevel,"max-overall-download-limit":e.maxOverallDownloadLimit,"max-overall-upload-limit":e.maxOverallUploadLimit,"max-download-result":e.maxDownloadResult.toString(),"uri-selector":e.uriSelector,"allow-overwrite":e.allowOverwrite?"true":"false","auto-file-renaming":e.autoFileRenaming?"true":"false",checksum:e.checksumAlgorithm,"event-poll":e.eventPoll,"checksum-check":e.checksumCheck?"true":"false"};e.log&&(n.log=e.log),e.rpcSecret&&(n["rpc-secret"]=e.rpcSecret),await d.changeGlobalOptions(n),m.success("设置保存成功")}catch(n){m.error("保存设置失败"),console.error("Failed to save advanced settings:",n)}finally{k.value=!1}}function U(){e.diskCache=16,e.fileAllocation="prealloc",e.checkIntegrity=!1,e.realtimeChunkChecksum=!0,e.logLevel="warn",e.log="",e.consoleLogLevel="notice",e.maxOverallDownloadLimit="0",e.maxOverallUploadLimit="0",e.maxDownloadResult=1e3,e.uriSelector="feedback",e.rpcSecret="",e.allowOverwrite=!1,e.autoFileRenaming=!0,e.checksumAlgorithm="sha-1",e.eventPoll="epoll",e.checksumCheck=!0,m.info("已重置为默认值")}return(n,l)=>{const C=u("el-input-number"),r=u("el-form-item"),s=u("el-option"),p=u("el-select"),v=u("el-switch"),f=u("el-card"),g=u("el-input"),b=u("el-button"),O=u("el-space"),R=u("el-form"),D=E("loading");return y(),F("div",q,[l[45]||(l[45]=i("div",{class:"settings-header"},[i("h2",null,"高级设置"),i("p",{class:"settings-description"},"配置 Aria2 的高级参数和性能选项")],-1)),H((y(),M(R,{ref_key:"formRef",ref:A,model:e,"label-width":"200px",style:{"max-width":"800px"}},{default:o(()=>[a(f,{class:"setting-group"},{header:o(()=>[...l[17]||(l[17]=[i("span",{class:"group-title"},"性能设置",-1)])]),default:o(()=>[a(r,{label:"磁盘缓存"},{default:o(()=>[a(C,{modelValue:e.diskCache,"onUpdate:modelValue":l[0]||(l[0]=t=>e.diskCache=t),min:0,max:1024,style:{width:"200px"}},null,8,["modelValue"]),l[18]||(l[18]=i("span",{style:{"margin-left":"8px"}},"MB",-1)),l[19]||(l[19]=i("div",{class:"form-tip"},"磁盘缓存大小，0 表示禁用",-1))]),_:1}),a(r,{label:"文件预分配"},{default:o(()=>[a(p,{modelValue:e.fileAllocation,"onUpdate:modelValue":l[1]||(l[1]=t=>e.fileAllocation=t),style:{width:"200px"}},{default:o(()=>[a(s,{label:"无",value:"none"}),a(s,{label:"预分配",value:"prealloc"}),a(s,{label:"回退",value:"falloc"})]),_:1},8,["modelValue"]),l[20]||(l[20]=i("div",{class:"form-tip"},"文件预分配方法",-1))]),_:1}),a(r,{label:"检查完整性"},{default:o(()=>[a(v,{modelValue:e.checkIntegrity,"onUpdate:modelValue":l[2]||(l[2]=t=>e.checkIntegrity=t)},null,8,["modelValue"]),l[21]||(l[21]=i("div",{class:"form-tip"},"下载完成后检查文件完整性",-1))]),_:1}),a(r,{label:"实时检查"},{default:o(()=>[a(v,{modelValue:e.realtimeChunkChecksum,"onUpdate:modelValue":l[3]||(l[3]=t=>e.realtimeChunkChecksum=t)},null,8,["modelValue"]),l[22]||(l[22]=i("div",{class:"form-tip"},"实时检查数据块校验和",-1))]),_:1})]),_:1}),a(f,{class:"setting-group"},{header:o(()=>[...l[23]||(l[23]=[i("span",{class:"group-title"},"日志设置",-1)])]),default:o(()=>[a(r,{label:"日志级别"},{default:o(()=>[a(p,{modelValue:e.logLevel,"onUpdate:modelValue":l[4]||(l[4]=t=>e.logLevel=t),style:{width:"200px"}},{default:o(()=>[a(s,{label:"调试",value:"debug"}),a(s,{label:"信息",value:"info"}),a(s,{label:"通知",value:"notice"}),a(s,{label:"警告",value:"warn"}),a(s,{label:"错误",value:"error"})]),_:1},8,["modelValue"]),l[24]||(l[24]=i("div",{class:"form-tip"},"日志记录级别",-1))]),_:1}),a(r,{label:"日志文件"},{default:o(()=>[a(g,{modelValue:e.log,"onUpdate:modelValue":l[5]||(l[5]=t=>e.log=t),placeholder:"日志文件路径"},null,8,["modelValue"]),l[25]||(l[25]=i("div",{class:"form-tip"},"日志文件保存路径",-1))]),_:1}),a(r,{label:"控制台日志级别"},{default:o(()=>[a(p,{modelValue:e.consoleLogLevel,"onUpdate:modelValue":l[6]||(l[6]=t=>e.consoleLogLevel=t),style:{width:"200px"}},{default:o(()=>[a(s,{label:"调试",value:"debug"}),a(s,{label:"信息",value:"info"}),a(s,{label:"通知",value:"notice"}),a(s,{label:"警告",value:"warn"}),a(s,{label:"错误",value:"error"})]),_:1},8,["modelValue"]),l[26]||(l[26]=i("div",{class:"form-tip"},"控制台日志级别",-1))]),_:1})]),_:1}),a(f,{class:"setting-group"},{header:o(()=>[...l[27]||(l[27]=[i("span",{class:"group-title"},"网络设置",-1)])]),default:o(()=>[a(r,{label:"最大整体下载速度"},{default:o(()=>[a(g,{modelValue:e.maxOverallDownloadLimit,"onUpdate:modelValue":l[7]||(l[7]=t=>e.maxOverallDownloadLimit=t),placeholder:"0 表示无限制",style:{width:"200px"}},null,8,["modelValue"]),l[28]||(l[28]=i("span",{style:{"margin-left":"8px"}},"KB/s",-1)),l[29]||(l[29]=i("div",{class:"form-tip"},"全局最大下载速度限制",-1))]),_:1}),a(r,{label:"最大整体上传速度"},{default:o(()=>[a(g,{modelValue:e.maxOverallUploadLimit,"onUpdate:modelValue":l[8]||(l[8]=t=>e.maxOverallUploadLimit=t),placeholder:"0 表示无限制",style:{width:"200px"}},null,8,["modelValue"]),l[30]||(l[30]=i("span",{style:{"margin-left":"8px"}},"KB/s",-1)),l[31]||(l[31]=i("div",{class:"form-tip"},"全局最大上传速度限制",-1))]),_:1}),a(r,{label:"最大下载结果"},{default:o(()=>[a(C,{modelValue:e.maxDownloadResult,"onUpdate:modelValue":l[9]||(l[9]=t=>e.maxDownloadResult=t),min:0,max:1e3,style:{width:"200px"}},null,8,["modelValue"]),l[32]||(l[32]=i("div",{class:"form-tip"},"保存的最大下载结果数量",-1))]),_:1}),a(r,{label:"URI 选择器"},{default:o(()=>[a(p,{modelValue:e.uriSelector,"onUpdate:modelValue":l[10]||(l[10]=t=>e.uriSelector=t),style:{width:"200px"}},{default:o(()=>[a(s,{label:"反馈",value:"feedback"}),a(s,{label:"顺序",value:"inorder"}),a(s,{label:"自适应",value:"adaptive"})]),_:1},8,["modelValue"]),l[33]||(l[33]=i("div",{class:"form-tip"},"URI 选择策略",-1))]),_:1})]),_:1}),a(f,{class:"setting-group"},{header:o(()=>[...l[34]||(l[34]=[i("span",{class:"group-title"},"安全设置",-1)])]),default:o(()=>[a(r,{label:"RPC 密钥"},{default:o(()=>[a(g,{modelValue:e.rpcSecret,"onUpdate:modelValue":l[11]||(l[11]=t=>e.rpcSecret=t),type:"password",placeholder:"RPC 访问密钥","show-password":""},null,8,["modelValue"]),l[35]||(l[35]=i("div",{class:"form-tip"},"RPC 接口访问密钥",-1))]),_:1}),a(r,{label:"允许覆盖"},{default:o(()=>[a(v,{modelValue:e.allowOverwrite,"onUpdate:modelValue":l[12]||(l[12]=t=>e.allowOverwrite=t)},null,8,["modelValue"]),l[36]||(l[36]=i("div",{class:"form-tip"},"允许覆盖已存在的文件",-1))]),_:1}),a(r,{label:"自动重命名"},{default:o(()=>[a(v,{modelValue:e.autoFileRenaming,"onUpdate:modelValue":l[13]||(l[13]=t=>e.autoFileRenaming=t)},null,8,["modelValue"]),l[37]||(l[37]=i("div",{class:"form-tip"},"自动重命名重复文件",-1))]),_:1})]),_:1}),a(f,{class:"setting-group"},{header:o(()=>[...l[38]||(l[38]=[i("span",{class:"group-title"},"其他设置",-1)])]),default:o(()=>[a(r,{label:"摘要算法"},{default:o(()=>[a(p,{modelValue:e.checksumAlgorithm,"onUpdate:modelValue":l[14]||(l[14]=t=>e.checksumAlgorithm=t),style:{width:"200px"}},{default:o(()=>[a(s,{label:"SHA-1",value:"sha-1"}),a(s,{label:"SHA-224",value:"sha-224"}),a(s,{label:"SHA-256",value:"sha-256"}),a(s,{label:"SHA-384",value:"sha-384"}),a(s,{label:"SHA-512",value:"sha-512"}),a(s,{label:"MD5",value:"md5"}),a(s,{label:"ADLER32",value:"adler32"})]),_:1},8,["modelValue"]),l[39]||(l[39]=i("div",{class:"form-tip"},"文件校验摘要算法",-1))]),_:1}),a(r,{label:"事件轮询"},{default:o(()=>[a(p,{modelValue:e.eventPoll,"onUpdate:modelValue":l[15]||(l[15]=t=>e.eventPoll=t),style:{width:"200px"}},{default:o(()=>[a(s,{label:"epoll",value:"epoll"}),a(s,{label:"kqueue",value:"kqueue"}),a(s,{label:"port",value:"port"}),a(s,{label:"poll",value:"poll"}),a(s,{label:"select",value:"select"})]),_:1},8,["modelValue"]),l[40]||(l[40]=i("div",{class:"form-tip"},"事件轮询方法",-1))]),_:1}),a(r,{label:"摘要检查"},{default:o(()=>[a(v,{modelValue:e.checksumCheck,"onUpdate:modelValue":l[16]||(l[16]=t=>e.checksumCheck=t)},null,8,["modelValue"]),l[41]||(l[41]=i("div",{class:"form-tip"},"启用文件摘要检查",-1))]),_:1})]),_:1}),a(r,{style:{"margin-top":"24px"}},{default:o(()=>[a(O,null,{default:o(()=>[a(b,{type:"primary",onClick:S,disabled:!L(d).isConnected,loading:k.value},{default:o(()=>[...l[42]||(l[42]=[V(" 保存设置 ",-1)])]),_:1},8,["disabled","loading"]),a(b,{onClick:x,disabled:!L(d).isConnected},{default:o(()=>[...l[43]||(l[43]=[V(" 重新加载 ",-1)])]),_:1},8,["disabled"]),a(b,{onClick:U},{default:o(()=>[...l[44]||(l[44]=[V(" 重置为默认值 ",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[D,c.value]])])}}});const j=N(G,[["__scopeId","data-v-6f5baaeb"]]);export{j as default};
