/* 深色主题样式 */

/* 全局深色主题变量 */
html.dark {
  --el-bg-color: #1a1a1a;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color-overlay: #1d1e1f;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-fill-color: #303133;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-dark: #39393a;
  --el-fill-color-darker: #424243;
  --el-fill-color-blank: transparent;
}

/* 基础元素深色主题 */
html.dark {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
}

html.dark body {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
}

html.dark #app {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
}

/* 应用容器深色主题 */
html.dark .app-container {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary);
}

/* 头部深色主题 */
html.dark .app-header {
  background: var(--el-bg-color) !important;
  border-bottom: 1px solid var(--el-border-color) !important;
  color: var(--el-text-color-primary);
}

html.dark .logo-text {
  color: #409eff !important;
}

html.dark .version {
  color: var(--el-text-color-secondary) !important;
  background: var(--el-fill-color) !important;
}

/* 侧边栏深色主题 */
html.dark .app-sidebar {
  background: var(--el-bg-color) !important;
  border-right: 1px solid var(--el-border-color) !important;
}

html.dark .sidebar-header h3 {
  color: var(--el-text-color-primary) !important;
}

/* 内容区域深色主题 */
html.dark .content-container {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary);
}

/* 底部状态栏深色主题 */
html.dark .app-footer {
  background: var(--el-bg-color) !important;
  border-top: 1px solid var(--el-border-color) !important;
  color: var(--el-text-color-primary);
}

html.dark .footer-section {
  color: var(--el-text-color-regular) !important;
}

html.dark .connection-status {
  color: var(--el-text-color-primary) !important;
}

/* 表格深色主题 */
html.dark .el-table {
  --el-table-bg-color: var(--el-bg-color);
  --el-table-tr-bg-color: var(--el-bg-color);
  --el-table-expanded-cell-bg-color: var(--el-fill-color-light);
}

html.dark .el-table th.el-table__cell {
  background-color: var(--el-fill-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-border-color) !important;
}

html.dark .el-table td.el-table__cell {
  border-bottom: 1px solid var(--el-border-color-extra-light) !important;
  color: var(--el-text-color-regular) !important;
}

html.dark .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: var(--el-fill-color-extra-light) !important;
}

html.dark .el-table__body tr:hover > td.el-table__cell {
  background-color: var(--el-fill-color-light) !important;
}

/* 卡片深色主题 */
html.dark .el-card {
  --el-card-bg-color: var(--el-bg-color);
  --el-card-border-color: var(--el-border-color);
  color: var(--el-text-color-primary);
}

html.dark .el-card__header {
  background-color: var(--el-fill-color) !important;
  border-bottom: 1px solid var(--el-border-color) !important;
  color: var(--el-text-color-primary) !important;
}

/* 表单深色主题 */
html.dark .el-form-item__label {
  color: var(--el-text-color-primary) !important;
}

html.dark .el-input__wrapper {
  background-color: var(--el-fill-color) !important;
  border: 1px solid var(--el-border-color) !important;
}

html.dark .el-input__inner {
  color: var(--el-text-color-primary) !important;
  background-color: transparent !important;
}

html.dark .el-input__inner::placeholder {
  color: var(--el-text-color-placeholder) !important;
}

html.dark .el-textarea__inner {
  background-color: var(--el-fill-color) !important;
  border: 1px solid var(--el-border-color) !important;
  color: var(--el-text-color-primary) !important;
}

html.dark .el-select .el-input__wrapper {
  background-color: var(--el-fill-color) !important;
}

/* 按钮深色主题 */
html.dark .el-button {
  --el-button-bg-color: var(--el-fill-color);
  --el-button-border-color: var(--el-border-color);
  --el-button-text-color: var(--el-text-color-primary);
}

html.dark .el-button:hover {
  --el-button-hover-bg-color: var(--el-fill-color-light);
  --el-button-hover-border-color: var(--el-border-color-light);
}

/* 对话框深色主题 */
html.dark .el-dialog {
  --el-dialog-bg-color: var(--el-bg-color);
  --el-dialog-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.24);
}

html.dark .el-dialog__header {
  border-bottom: 1px solid var(--el-border-color) !important;
}

html.dark .el-dialog__title {
  color: var(--el-text-color-primary) !important;
}

html.dark .el-dialog__body {
  color: var(--el-text-color-regular) !important;
}

/* 菜单深色主题 */
html.dark .el-menu {
  --el-menu-bg-color: var(--el-bg-color);
  --el-menu-text-color: var(--el-text-color-regular);
  --el-menu-hover-bg-color: var(--el-fill-color-light);
  --el-menu-active-color: #409eff;
  border-right: 1px solid var(--el-border-color) !important;
}

html.dark .el-menu-item {
  color: var(--el-text-color-regular) !important;
}

html.dark .el-menu-item:hover {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-primary) !important;
}

html.dark .el-menu-item.is-active {
  background-color: var(--el-fill-color-dark) !important;
  color: #409eff !important;
}

html.dark .el-sub-menu__title {
  color: var(--el-text-color-regular) !important;
}

html.dark .el-sub-menu__title:hover {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-primary) !important;
}

/* 进度条深色主题 */
html.dark .el-progress-bar__outer {
  background-color: var(--el-fill-color) !important;
}

/* 标签深色主题 */
html.dark .el-tag {
  --el-tag-bg-color: var(--el-fill-color);
  --el-tag-border-color: var(--el-border-color);
  --el-tag-text-color: var(--el-text-color-primary);
}

/* 分割线深色主题 */
html.dark .el-divider {
  --el-border-color: var(--el-border-color);
}

/* 消息提示深色主题 */
html.dark .el-message {
  --el-message-bg-color: var(--el-bg-color-overlay);
  --el-message-border-color: var(--el-border-color);
  --el-message-text-color: var(--el-text-color-primary);
}

/* 加载深色主题 */
html.dark .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* 自定义组件深色主题 */
html.dark .task-action-btn {
  background: var(--el-fill-color) !important;
  border: 1px solid var(--el-border-color) !important;
  color: var(--el-text-color-primary) !important;
}

html.dark .task-action-btn:hover {
  background: var(--el-fill-color-light) !important;
  border-color: var(--el-border-color-light) !important;
}

/* 滚动条深色主题 */
html.dark ::-webkit-scrollbar-track {
  background: var(--el-fill-color-extra-light) !important;
}

html.dark ::-webkit-scrollbar-thumb {
  background: var(--el-fill-color-dark) !important;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--el-fill-color-darker) !important;
}

/* 设置页面深色主题 */
html.dark .general-settings {
  color: var(--el-text-color-primary);
}

html.dark .settings-header h2 {
  color: var(--el-text-color-primary) !important;
}

html.dark .settings-description {
  color: var(--el-text-color-secondary) !important;
}

html.dark .form-tip {
  color: var(--el-text-color-secondary) !important;
}
