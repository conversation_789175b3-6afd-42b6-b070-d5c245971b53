import{d as M,x as B,r as V,a as D,L as F,E as p,g as q,l as o,y as A,f as H,k as r,e as i,B as N,o as U,m as l,u as P,p as T,i as G}from"./index-a49529e1.js";const O={class:"bt-settings"},K=M({__name:"Aria2BtSettings",setup(X){const d=B(),L=V(),f=V(!1),v=V(!1),t=D({listenPort:6881,dhtListenPort:6881,enableDht:!0,enablePeerExchange:!0,btEnableLpd:!1,btMaxPeers:55,seedRatio:1,seedTime:0,maxUploadLimit:"0",btTrackerTimeout:60,btTrackerInterval:0,btTracker:"",btRequireCrypto:!1,btMinCryptoLevel:"plain",btSaveMetadata:!1,btRemoveUnselectedFile:!1,btExternalIp:""});F(()=>{d.isConnected&&y()});async function y(){if(!d.isConnected){p.warning("请先连接到 Aria2 服务器");return}f.value=!0;try{const n=await d.getGlobalOptions();n&&(t.listenPort=parseInt(n["listen-port"]||"6881"),t.dhtListenPort=parseInt(n["dht-listen-port"]||"6881"),t.enableDht=n["enable-dht"]!=="false",t.enablePeerExchange=n["enable-peer-exchange"]!=="false",t.btEnableLpd=n["bt-enable-lpd"]==="true",t.btMaxPeers=parseInt(n["bt-max-peers"]||"55"),t.seedRatio=parseFloat(n["seed-ratio"]||"1.0"),t.seedTime=parseInt(n["seed-time"]||"0"),t.maxUploadLimit=n["max-upload-limit"]||"0",t.btTrackerTimeout=parseInt(n["bt-tracker-timeout"]||"60"),t.btTrackerInterval=parseInt(n["bt-tracker-interval"]||"0"),t.btTracker=n["bt-tracker"]||"",t.btRequireCrypto=n["bt-require-crypto"]==="true",t.btMinCryptoLevel=n["bt-min-crypto-level"]||"plain",t.btSaveMetadata=n["bt-save-metadata"]==="true",t.btRemoveUnselectedFile=n["bt-remove-unselected-file"]==="true",t.btExternalIp=n["bt-external-ip"]||""),p.success("设置加载成功")}catch(n){p.error("加载设置失败"),console.error("Failed to load BT settings:",n)}finally{f.value=!1}}async function C(){if(!d.isConnected){p.warning("请先连接到 Aria2 服务器");return}v.value=!0;try{const n={"listen-port":t.listenPort.toString(),"dht-listen-port":t.dhtListenPort.toString(),"enable-dht":t.enableDht?"true":"false","enable-peer-exchange":t.enablePeerExchange?"true":"false","bt-enable-lpd":t.btEnableLpd?"true":"false","bt-max-peers":t.btMaxPeers.toString(),"seed-ratio":t.seedRatio.toString(),"seed-time":t.seedTime.toString(),"max-upload-limit":t.maxUploadLimit,"bt-tracker-timeout":t.btTrackerTimeout.toString(),"bt-tracker-interval":t.btTrackerInterval.toString(),"bt-require-crypto":t.btRequireCrypto?"true":"false","bt-min-crypto-level":t.btMinCryptoLevel,"bt-save-metadata":t.btSaveMetadata?"true":"false","bt-remove-unselected-file":t.btRemoveUnselectedFile?"true":"false"};t.btTracker&&(n["bt-tracker"]=t.btTracker),t.btExternalIp&&(n["bt-external-ip"]=t.btExternalIp),await d.changeGlobalOptions(n),p.success("设置保存成功")}catch(n){p.error("保存设置失败"),console.error("Failed to save BT settings:",n)}finally{v.value=!1}}function E(){t.listenPort=6881,t.dhtListenPort=6881,t.enableDht=!0,t.enablePeerExchange=!0,t.btEnableLpd=!1,t.btMaxPeers=55,t.seedRatio=1,t.seedTime=0,t.maxUploadLimit="0",t.btTrackerTimeout=60,t.btTrackerInterval=0,t.btTracker="",t.btRequireCrypto=!1,t.btMinCryptoLevel="plain",t.btSaveMetadata=!1,t.btRemoveUnselectedFile=!1,t.btExternalIp="",p.info("已重置为默认值")}return(n,e)=>{const u=i("el-input-number"),s=i("el-form-item"),m=i("el-switch"),b=i("el-card"),x=i("el-input"),k=i("el-option"),I=i("el-select"),g=i("el-button"),w=i("el-space"),R=i("el-form"),S=N("loading");return U(),q("div",O,[e[45]||(e[45]=o("div",{class:"settings-header"},[o("h2",null,"BitTorrent 设置"),o("p",{class:"settings-description"},"配置 BitTorrent 下载相关参数")],-1)),A((U(),H(R,{ref_key:"formRef",ref:L,model:t,"label-width":"200px",style:{"max-width":"800px"}},{default:r(()=>[l(b,{class:"setting-group"},{header:r(()=>[...e[17]||(e[17]=[o("span",{class:"group-title"},"基本设置",-1)])]),default:r(()=>[l(s,{label:"监听端口"},{default:r(()=>[l(u,{modelValue:t.listenPort,"onUpdate:modelValue":e[0]||(e[0]=a=>t.listenPort=a),min:1024,max:65535,style:{width:"200px"}},null,8,["modelValue"]),e[18]||(e[18]=o("div",{class:"form-tip"},"BitTorrent 监听端口",-1))]),_:1}),l(s,{label:"DHT 监听端口"},{default:r(()=>[l(u,{modelValue:t.dhtListenPort,"onUpdate:modelValue":e[1]||(e[1]=a=>t.dhtListenPort=a),min:1024,max:65535,style:{width:"200px"}},null,8,["modelValue"]),e[19]||(e[19]=o("div",{class:"form-tip"},"DHT 网络监听端口",-1))]),_:1}),l(s,{label:"启用 DHT"},{default:r(()=>[l(m,{modelValue:t.enableDht,"onUpdate:modelValue":e[2]||(e[2]=a=>t.enableDht=a)},null,8,["modelValue"]),e[20]||(e[20]=o("div",{class:"form-tip"},"启用分布式哈希表",-1))]),_:1}),l(s,{label:"启用 PEX"},{default:r(()=>[l(m,{modelValue:t.enablePeerExchange,"onUpdate:modelValue":e[3]||(e[3]=a=>t.enablePeerExchange=a)},null,8,["modelValue"]),e[21]||(e[21]=o("div",{class:"form-tip"},"启用对等交换",-1))]),_:1}),l(s,{label:"启用 LPD"},{default:r(()=>[l(m,{modelValue:t.btEnableLpd,"onUpdate:modelValue":e[4]||(e[4]=a=>t.btEnableLpd=a)},null,8,["modelValue"]),e[22]||(e[22]=o("div",{class:"form-tip"},"启用本地对等发现",-1))]),_:1})]),_:1}),l(b,{class:"setting-group"},{header:r(()=>[...e[23]||(e[23]=[o("span",{class:"group-title"},"连接设置",-1)])]),default:r(()=>[l(s,{label:"最大连接数"},{default:r(()=>[l(u,{modelValue:t.btMaxPeers,"onUpdate:modelValue":e[5]||(e[5]=a=>t.btMaxPeers=a),min:1,max:1e3,style:{width:"200px"}},null,8,["modelValue"]),e[24]||(e[24]=o("div",{class:"form-tip"},"每个种子的最大连接数",-1))]),_:1}),l(s,{label:"最小分享率"},{default:r(()=>[l(u,{modelValue:t.seedRatio,"onUpdate:modelValue":e[6]||(e[6]=a=>t.seedRatio=a),min:0,max:100,step:.1,precision:1,style:{width:"200px"}},null,8,["modelValue"]),e[25]||(e[25]=o("div",{class:"form-tip"},"达到此分享率后停止做种",-1))]),_:1}),l(s,{label:"做种时间"},{default:r(()=>[l(u,{modelValue:t.seedTime,"onUpdate:modelValue":e[7]||(e[7]=a=>t.seedTime=a),min:0,max:999999,style:{width:"200px"}},null,8,["modelValue"]),e[26]||(e[26]=o("span",{style:{"margin-left":"8px"}},"分钟",-1)),e[27]||(e[27]=o("div",{class:"form-tip"},"做种时间限制，0 表示无限制",-1))]),_:1}),l(s,{label:"最大上传速度"},{default:r(()=>[l(x,{modelValue:t.maxUploadLimit,"onUpdate:modelValue":e[8]||(e[8]=a=>t.maxUploadLimit=a),placeholder:"0 表示无限制",style:{width:"200px"}},null,8,["modelValue"]),e[28]||(e[28]=o("span",{style:{"margin-left":"8px"}},"KB/s",-1)),e[29]||(e[29]=o("div",{class:"form-tip"},"全局最大上传速度限制",-1))]),_:1})]),_:1}),l(b,{class:"setting-group"},{header:r(()=>[...e[30]||(e[30]=[o("span",{class:"group-title"},"Tracker 设置",-1)])]),default:r(()=>[l(s,{label:"Tracker 超时"},{default:r(()=>[l(u,{modelValue:t.btTrackerTimeout,"onUpdate:modelValue":e[9]||(e[9]=a=>t.btTrackerTimeout=a),min:1,max:600,style:{width:"200px"}},null,8,["modelValue"]),e[31]||(e[31]=o("span",{style:{"margin-left":"8px"}},"秒",-1)),e[32]||(e[32]=o("div",{class:"form-tip"},"Tracker 请求超时时间",-1))]),_:1}),l(s,{label:"Tracker 间隔"},{default:r(()=>[l(u,{modelValue:t.btTrackerInterval,"onUpdate:modelValue":e[10]||(e[10]=a=>t.btTrackerInterval=a),min:0,max:86400,style:{width:"200px"}},null,8,["modelValue"]),e[33]||(e[33]=o("span",{style:{"margin-left":"8px"}},"秒",-1)),e[34]||(e[34]=o("div",{class:"form-tip"},"Tracker 请求间隔时间",-1))]),_:1}),l(s,{label:"额外 Tracker"},{default:r(()=>[l(x,{modelValue:t.btTracker,"onUpdate:modelValue":e[11]||(e[11]=a=>t.btTracker=a),type:"textarea",rows:6,placeholder:"额外的 Tracker 服务器，每行一个"},null,8,["modelValue"]),e[35]||(e[35]=o("div",{class:"form-tip"},"额外的 Tracker 服务器列表",-1))]),_:1})]),_:1}),l(b,{class:"setting-group"},{header:r(()=>[...e[36]||(e[36]=[o("span",{class:"group-title"},"高级设置",-1)])]),default:r(()=>[l(s,{label:"强制加密"},{default:r(()=>[l(m,{modelValue:t.btRequireCrypto,"onUpdate:modelValue":e[12]||(e[12]=a=>t.btRequireCrypto=a)},null,8,["modelValue"]),e[37]||(e[37]=o("div",{class:"form-tip"},"强制使用加密连接",-1))]),_:1}),l(s,{label:"最小加密级别"},{default:r(()=>[l(I,{modelValue:t.btMinCryptoLevel,"onUpdate:modelValue":e[13]||(e[13]=a=>t.btMinCryptoLevel=a),style:{width:"200px"}},{default:r(()=>[l(k,{label:"纯文本",value:"plain"}),l(k,{label:"ARC4",value:"arc4"})]),_:1},8,["modelValue"]),e[38]||(e[38]=o("div",{class:"form-tip"},"最小加密级别",-1))]),_:1}),l(s,{label:"保存元数据"},{default:r(()=>[l(m,{modelValue:t.btSaveMetadata,"onUpdate:modelValue":e[14]||(e[14]=a=>t.btSaveMetadata=a)},null,8,["modelValue"]),e[39]||(e[39]=o("div",{class:"form-tip"},"保存种子元数据为 .torrent 文件",-1))]),_:1}),l(s,{label:"移除未选择文件"},{default:r(()=>[l(m,{modelValue:t.btRemoveUnselectedFile,"onUpdate:modelValue":e[15]||(e[15]=a=>t.btRemoveUnselectedFile=a)},null,8,["modelValue"]),e[40]||(e[40]=o("div",{class:"form-tip"},"移除未选择下载的文件",-1))]),_:1}),l(s,{label:"外部 IP"},{default:r(()=>[l(x,{modelValue:t.btExternalIp,"onUpdate:modelValue":e[16]||(e[16]=a=>t.btExternalIp=a),placeholder:"自动检测",style:{width:"200px"}},null,8,["modelValue"]),e[41]||(e[41]=o("div",{class:"form-tip"},"向 Tracker 报告的外部 IP 地址",-1))]),_:1})]),_:1}),l(s,{style:{"margin-top":"24px"}},{default:r(()=>[l(w,null,{default:r(()=>[l(g,{type:"primary",onClick:C,disabled:!P(d).isConnected,loading:v.value},{default:r(()=>[...e[42]||(e[42]=[T(" 保存设置 ",-1)])]),_:1},8,["disabled","loading"]),l(g,{onClick:y,disabled:!P(d).isConnected},{default:r(()=>[...e[43]||(e[43]=[T(" 重新加载 ",-1)])]),_:1},8,["disabled"]),l(g,{onClick:E},{default:r(()=>[...e[44]||(e[44]=[T(" 重置为默认值 ",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[S,f.value]])])}}});const z=G(K,[["__scopeId","data-v-7ab8021f"]]);export{z as default};
