import { createI18n } from 'vue-i18n'
import zhCN from '../locales/zh-CN'
import en from '../locales/en'

// 语言包
const messages = {
  'zh-CN': zhCN,
  'en': en
}

// 获取浏览器语言
function getBrowserLanguage(): string {
  const browserLang = navigator.language || 'zh-CN'
  
  // 支持的语言列表
  const supportedLanguages = ['zh-CN', 'en']
  
  // 精确匹配
  if (supportedLanguages.includes(browserLang)) {
    return browserLang
  }
  
  // 模糊匹配（例如 en-US -> en）
  const langCode = browserLang.split('-')[0]
  const matchedLang = supportedLanguages.find(lang => lang.startsWith(langCode))
  
  return matchedLang || 'zh-CN'
}

// 从设置中获取语言
function getLanguageFromSettings(): string {
  try {
    const savedSettings = localStorage.getItem('aria2-desktop-settings')
    if (savedSettings) {
      const settings = JSON.parse(savedSettings)
      return settings.language || getBrowserLanguage()
    }
  } catch (error) {
    console.error('Failed to get language from settings:', error)
  }
  
  return getBrowserLanguage()
}

// 创建i18n实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: getLanguageFromSettings(),
  fallbackLocale: 'zh-CN',
  messages,
  globalInjection: true // 全局注入 $t 函数
})

// 切换语言的函数
export function setLanguage(locale: string) {
  if (messages[locale as keyof typeof messages]) {
    i18n.global.locale.value = locale
    
    // 更新 HTML lang 属性
    document.documentElement.lang = locale
    
    return true
  }
  return false
}

// 获取当前语言
export function getCurrentLanguage(): string {
  return i18n.global.locale.value
}

// 获取支持的语言列表
export function getSupportedLanguages() {
  return [
    { label: '简体中文', value: 'zh-CN' },
    { label: 'English', value: 'en' }
  ]
}

export default i18n
