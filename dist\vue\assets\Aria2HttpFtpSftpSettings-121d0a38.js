import{d as M,x as A,r as v,a as R,L as z,E as f,g as I,l as r,y as B,f as L,k as a,e as d,B as W,o as U,m as l,u as g,p as w,i as N}from"./index-a49529e1.js";const E={class:"http-ftp-sftp-settings"},D=M({__name:"Aria2HttpFtpSftpSettings",setup(G){const u=A(),H=v(),P=v(!1),x=v(!1),e=R({userAgent:"",httpUser:"",httpPasswd:"",httpProxy:"",httpsProxy:"",httpProxyUser:"",httpProxyPasswd:"",header:"",cookie:"",referer:"",checkCertificate:!0,caCertificate:"",certificate:"",privateKey:"",ftpUser:"",ftpPasswd:"",ftpProxy:"",ftpProxyUser:"",ftpProxyPasswd:"",ftpType:"binary",ftpPasv:!0,ftpReuseConnection:!0,sshHostKeyMd:"",sshPrivateKey:"",sshPublicKey:"",sshKeyPassphrase:"",connectTimeout:60,timeout:60,maxTries:5,retryWait:0,lowestSpeedLimit:"0",minSplitSize:"20M",maxConnectionPerServer:1,split:5});z(()=>{u.isConnected&&S()});async function S(){if(!u.isConnected){f.warning("请先连接到 Aria2 服务器");return}P.value=!0;try{const o=await u.getGlobalOptions();o&&(e.userAgent=o["user-agent"]||"",e.httpUser=o["http-user"]||"",e.httpPasswd=o["http-passwd"]||"",e.httpProxy=o["http-proxy"]||"",e.httpsProxy=o["https-proxy"]||"",e.httpProxyUser=o["http-proxy-user"]||"",e.httpProxyPasswd=o["http-proxy-passwd"]||"",e.header=o.header||"",e.cookie=o.cookie||"",e.referer=o.referer||"",e.checkCertificate=o["check-certificate"]!=="false",e.caCertificate=o["ca-certificate"]||"",e.certificate=o.certificate||"",e.privateKey=o["private-key"]||"",e.ftpUser=o["ftp-user"]||"",e.ftpPasswd=o["ftp-passwd"]||"",e.ftpProxy=o["ftp-proxy"]||"",e.ftpProxyUser=o["ftp-proxy-user"]||"",e.ftpProxyPasswd=o["ftp-proxy-passwd"]||"",e.ftpType=o["ftp-type"]||"binary",e.ftpPasv=o["ftp-pasv"]!=="false",e.ftpReuseConnection=o["ftp-reuse-connection"]!=="false",e.sshHostKeyMd=o["ssh-host-key-md"]||"",e.sshPrivateKey=o["ssh-private-key"]||"",e.sshPublicKey=o["ssh-public-key"]||"",e.sshKeyPassphrase=o["ssh-key-passphrase"]||"",e.connectTimeout=parseInt(o["connect-timeout"]||"60"),e.timeout=parseInt(o.timeout||"60"),e.maxTries=parseInt(o["max-tries"]||"5"),e.retryWait=parseInt(o["retry-wait"]||"0"),e.lowestSpeedLimit=o["lowest-speed-limit"]||"0",e.minSplitSize=o["min-split-size"]||"20M",e.maxConnectionPerServer=parseInt(o["max-connection-per-server"]||"1"),e.split=parseInt(o.split||"5")),f.success("设置加载成功")}catch(o){f.error("加载设置失败"),console.error("Failed to load HTTP/FTP/SFTP settings:",o)}finally{P.value=!1}}async function C(){if(!u.isConnected){f.warning("请先连接到 Aria2 服务器");return}x.value=!0;try{const o={"check-certificate":e.checkCertificate?"true":"false","ftp-type":e.ftpType,"ftp-pasv":e.ftpPasv?"true":"false","ftp-reuse-connection":e.ftpReuseConnection?"true":"false","connect-timeout":e.connectTimeout.toString(),timeout:e.timeout.toString(),"max-tries":e.maxTries.toString(),"retry-wait":e.retryWait.toString(),"lowest-speed-limit":e.lowestSpeedLimit,"min-split-size":e.minSplitSize,"max-connection-per-server":e.maxConnectionPerServer.toString(),split:e.split.toString()};e.userAgent&&(o["user-agent"]=e.userAgent),e.httpUser&&(o["http-user"]=e.httpUser),e.httpPasswd&&(o["http-passwd"]=e.httpPasswd),e.httpProxy&&(o["http-proxy"]=e.httpProxy),e.httpsProxy&&(o["https-proxy"]=e.httpsProxy),e.httpProxyUser&&(o["http-proxy-user"]=e.httpProxyUser),e.httpProxyPasswd&&(o["http-proxy-passwd"]=e.httpProxyPasswd),e.header&&(o.header=e.header),e.cookie&&(o.cookie=e.cookie),e.referer&&(o.referer=e.referer),e.caCertificate&&(o["ca-certificate"]=e.caCertificate),e.certificate&&(o.certificate=e.certificate),e.privateKey&&(o["private-key"]=e.privateKey),e.ftpUser&&(o["ftp-user"]=e.ftpUser),e.ftpPasswd&&(o["ftp-passwd"]=e.ftpPasswd),e.ftpProxy&&(o["ftp-proxy"]=e.ftpProxy),e.ftpProxyUser&&(o["ftp-proxy-user"]=e.ftpProxyUser),e.ftpProxyPasswd&&(o["ftp-proxy-passwd"]=e.ftpProxyPasswd),e.sshHostKeyMd&&(o["ssh-host-key-md"]=e.sshHostKeyMd),e.sshPrivateKey&&(o["ssh-private-key"]=e.sshPrivateKey),e.sshPublicKey&&(o["ssh-public-key"]=e.sshPublicKey),e.sshKeyPassphrase&&(o["ssh-key-passphrase"]=e.sshKeyPassphrase),await u.changeGlobalOptions(o),f.success("设置保存成功")}catch(o){f.error("保存设置失败"),console.error("Failed to save HTTP/FTP/SFTP settings:",o)}finally{x.value=!1}}function k(){e.userAgent="",e.httpUser="",e.httpPasswd="",e.httpProxy="",e.httpsProxy="",e.httpProxyUser="",e.httpProxyPasswd="",e.header="",e.cookie="",e.referer="",e.checkCertificate=!0,e.caCertificate="",e.certificate="",e.privateKey="",e.ftpUser="",e.ftpPasswd="",e.ftpProxy="",e.ftpProxyUser="",e.ftpProxyPasswd="",e.ftpType="binary",e.ftpPasv=!0,e.ftpReuseConnection=!0,e.sshHostKeyMd="",e.sshPrivateKey="",e.sshPublicKey="",e.sshKeyPassphrase="",e.connectTimeout=60,e.timeout=60,e.maxTries=5,e.retryWait=0,e.lowestSpeedLimit="0",e.minSplitSize="20M",e.maxConnectionPerServer=1,e.split=5,f.info("已重置为默认值")}return(o,t)=>{const i=d("el-input"),p=d("el-form-item"),V=d("el-switch"),y=d("el-card"),n=d("el-option"),b=d("el-select"),m=d("el-input-number"),T=d("el-button"),K=d("el-space"),h=d("el-form"),F=W("loading");return U(),I("div",E,[t[71]||(t[71]=r("div",{class:"settings-header"},[r("h2",null,"HTTP/FTP/SFTP 设置"),r("p",{class:"settings-description"},"配置 HTTP、FTP 和 SFTP 协议的综合参数")],-1)),B((U(),L(h,{ref_key:"formRef",ref:H,model:e,"label-width":"200px",style:{"max-width":"800px"}},{default:a(()=>[l(y,{class:"setting-group"},{header:a(()=>[...t[34]||(t[34]=[r("span",{class:"group-title"},"HTTP/HTTPS 设置",-1)])]),default:a(()=>[l(p,{label:"用户代理"},{default:a(()=>[l(i,{modelValue:e.userAgent,"onUpdate:modelValue":t[0]||(t[0]=s=>e.userAgent=s),placeholder:"自定义 User-Agent"},null,8,["modelValue"]),t[35]||(t[35]=r("div",{class:"form-tip"},"HTTP 请求的 User-Agent 头",-1))]),_:1}),l(p,{label:"HTTP 认证用户名"},{default:a(()=>[l(i,{modelValue:e.httpUser,"onUpdate:modelValue":t[1]||(t[1]=s=>e.httpUser=s),placeholder:"HTTP 基本认证用户名"},null,8,["modelValue"])]),_:1}),l(p,{label:"HTTP 认证密码"},{default:a(()=>[l(i,{modelValue:e.httpPasswd,"onUpdate:modelValue":t[2]||(t[2]=s=>e.httpPasswd=s),type:"password",placeholder:"HTTP 基本认证密码","show-password":""},null,8,["modelValue"])]),_:1}),l(p,{label:"HTTP 代理"},{default:a(()=>[l(i,{modelValue:e.httpProxy,"onUpdate:modelValue":t[3]||(t[3]=s=>e.httpProxy=s),placeholder:"http://proxy.example.com:8080"},null,8,["modelValue"]),t[36]||(t[36]=r("div",{class:"form-tip"},"HTTP 代理服务器地址",-1))]),_:1}),l(p,{label:"HTTPS 代理"},{default:a(()=>[l(i,{modelValue:e.httpsProxy,"onUpdate:modelValue":t[4]||(t[4]=s=>e.httpsProxy=s),placeholder:"https://proxy.example.com:8080"},null,8,["modelValue"]),t[37]||(t[37]=r("div",{class:"form-tip"},"HTTPS 代理服务器地址",-1))]),_:1}),l(p,{label:"代理认证用户名"},{default:a(()=>[l(i,{modelValue:e.httpProxyUser,"onUpdate:modelValue":t[5]||(t[5]=s=>e.httpProxyUser=s),placeholder:"代理服务器用户名"},null,8,["modelValue"])]),_:1}),l(p,{label:"代理认证密码"},{default:a(()=>[l(i,{modelValue:e.httpProxyPasswd,"onUpdate:modelValue":t[6]||(t[6]=s=>e.httpProxyPasswd=s),type:"password",placeholder:"代理服务器密码","show-password":""},null,8,["modelValue"])]),_:1}),l(p,{label:"自定义请求头"},{default:a(()=>[l(i,{modelValue:e.header,"onUpdate:modelValue":t[7]||(t[7]=s=>e.header=s),type:"textarea",rows:4,placeholder:`自定义 HTTP 请求头，每行一个
格式：Header-Name: Header-Value

示例：
Authorization: Bearer token123
X-Custom-Header: custom-value`},null,8,["modelValue"]),t[38]||(t[38]=r("div",{class:"form-tip"},"自定义 HTTP 请求头，每行一个",-1))]),_:1}),l(p,{label:"Cookie"},{default:a(()=>[l(i,{modelValue:e.cookie,"onUpdate:modelValue":t[8]||(t[8]=s=>e.cookie=s),placeholder:"HTTP Cookie 字符串"},null,8,["modelValue"]),t[39]||(t[39]=r("div",{class:"form-tip"},"发送给服务器的 Cookie",-1))]),_:1}),l(p,{label:"Referer"},{default:a(()=>[l(i,{modelValue:e.referer,"onUpdate:modelValue":t[9]||(t[9]=s=>e.referer=s),placeholder:"HTTP Referer 头"},null,8,["modelValue"]),t[40]||(t[40]=r("div",{class:"form-tip"},"HTTP Referer 头的值",-1))]),_:1}),l(p,{label:"验证 HTTPS 证书"},{default:a(()=>[l(V,{modelValue:e.checkCertificate,"onUpdate:modelValue":t[10]||(t[10]=s=>e.checkCertificate=s)},null,8,["modelValue"]),t[41]||(t[41]=r("div",{class:"form-tip"},"是否验证 HTTPS 服务器证书",-1))]),_:1}),l(p,{label:"CA 证书文件"},{default:a(()=>[l(i,{modelValue:e.caCertificate,"onUpdate:modelValue":t[11]||(t[11]=s=>e.caCertificate=s),placeholder:"CA 证书文件路径"},null,8,["modelValue"]),t[42]||(t[42]=r("div",{class:"form-tip"},"用于验证 HTTPS 证书的 CA 证书文件",-1))]),_:1}),l(p,{label:"客户端证书"},{default:a(()=>[l(i,{modelValue:e.certificate,"onUpdate:modelValue":t[12]||(t[12]=s=>e.certificate=s),placeholder:"客户端证书文件路径"},null,8,["modelValue"]),t[43]||(t[43]=r("div",{class:"form-tip"},"HTTPS 客户端证书文件",-1))]),_:1}),l(p,{label:"私钥文件"},{default:a(()=>[l(i,{modelValue:e.privateKey,"onUpdate:modelValue":t[13]||(t[13]=s=>e.privateKey=s),placeholder:"私钥文件路径"},null,8,["modelValue"]),t[44]||(t[44]=r("div",{class:"form-tip"},"客户端证书的私钥文件",-1))]),_:1})]),_:1}),l(y,{class:"setting-group"},{header:a(()=>[...t[45]||(t[45]=[r("span",{class:"group-title"},"FTP 设置",-1)])]),default:a(()=>[l(p,{label:"FTP 用户名"},{default:a(()=>[l(i,{modelValue:e.ftpUser,"onUpdate:modelValue":t[14]||(t[14]=s=>e.ftpUser=s),placeholder:"FTP 登录用户名"},null,8,["modelValue"])]),_:1}),l(p,{label:"FTP 密码"},{default:a(()=>[l(i,{modelValue:e.ftpPasswd,"onUpdate:modelValue":t[15]||(t[15]=s=>e.ftpPasswd=s),type:"password",placeholder:"FTP 登录密码","show-password":""},null,8,["modelValue"])]),_:1}),l(p,{label:"FTP 代理"},{default:a(()=>[l(i,{modelValue:e.ftpProxy,"onUpdate:modelValue":t[16]||(t[16]=s=>e.ftpProxy=s),placeholder:"ftp://proxy.example.com:21"},null,8,["modelValue"]),t[46]||(t[46]=r("div",{class:"form-tip"},"FTP 代理服务器地址",-1))]),_:1}),l(p,{label:"FTP 代理用户名"},{default:a(()=>[l(i,{modelValue:e.ftpProxyUser,"onUpdate:modelValue":t[17]||(t[17]=s=>e.ftpProxyUser=s),placeholder:"FTP 代理用户名"},null,8,["modelValue"])]),_:1}),l(p,{label:"FTP 代理密码"},{default:a(()=>[l(i,{modelValue:e.ftpProxyPasswd,"onUpdate:modelValue":t[18]||(t[18]=s=>e.ftpProxyPasswd=s),type:"password",placeholder:"FTP 代理密码","show-password":""},null,8,["modelValue"])]),_:1}),l(p,{label:"FTP 传输类型"},{default:a(()=>[l(b,{modelValue:e.ftpType,"onUpdate:modelValue":t[19]||(t[19]=s=>e.ftpType=s),style:{width:"200px"}},{default:a(()=>[l(n,{label:"二进制模式",value:"binary"}),l(n,{label:"ASCII 模式",value:"ascii"})]),_:1},8,["modelValue"]),t[47]||(t[47]=r("div",{class:"form-tip"},"FTP 文件传输模式",-1))]),_:1}),l(p,{label:"被动模式"},{default:a(()=>[l(V,{modelValue:e.ftpPasv,"onUpdate:modelValue":t[20]||(t[20]=s=>e.ftpPasv=s)},null,8,["modelValue"]),t[48]||(t[48]=r("div",{class:"form-tip"},"使用 FTP 被动模式（推荐）",-1))]),_:1}),l(p,{label:"重用连接"},{default:a(()=>[l(V,{modelValue:e.ftpReuseConnection,"onUpdate:modelValue":t[21]||(t[21]=s=>e.ftpReuseConnection=s)},null,8,["modelValue"]),t[49]||(t[49]=r("div",{class:"form-tip"},"重用 FTP 连接以提高性能",-1))]),_:1})]),_:1}),l(y,{class:"setting-group"},{header:a(()=>[...t[50]||(t[50]=[r("span",{class:"group-title"},"SFTP/SSH 设置",-1)])]),default:a(()=>[l(p,{label:"SSH 主机密钥文件"},{default:a(()=>[l(i,{modelValue:e.sshHostKeyMd,"onUpdate:modelValue":t[22]||(t[22]=s=>e.sshHostKeyMd=s),placeholder:"SSH 主机密钥文件路径"},null,8,["modelValue"]),t[51]||(t[51]=r("div",{class:"form-tip"},"SSH 主机密钥验证文件",-1))]),_:1}),l(p,{label:"SSH 私钥文件"},{default:a(()=>[l(i,{modelValue:e.sshPrivateKey,"onUpdate:modelValue":t[23]||(t[23]=s=>e.sshPrivateKey=s),placeholder:"SSH 私钥文件路径"},null,8,["modelValue"]),t[52]||(t[52]=r("div",{class:"form-tip"},"SSH 私钥文件用于身份验证",-1))]),_:1}),l(p,{label:"SSH 公钥文件"},{default:a(()=>[l(i,{modelValue:e.sshPublicKey,"onUpdate:modelValue":t[24]||(t[24]=s=>e.sshPublicKey=s),placeholder:"SSH 公钥文件路径"},null,8,["modelValue"]),t[53]||(t[53]=r("div",{class:"form-tip"},"SSH 公钥文件",-1))]),_:1}),l(p,{label:"SSH 密钥密码"},{default:a(()=>[l(i,{modelValue:e.sshKeyPassphrase,"onUpdate:modelValue":t[25]||(t[25]=s=>e.sshKeyPassphrase=s),type:"password",placeholder:"SSH 私钥密码","show-password":""},null,8,["modelValue"]),t[54]||(t[54]=r("div",{class:"form-tip"},"SSH 私钥文件的密码",-1))]),_:1})]),_:1}),l(y,{class:"setting-group"},{header:a(()=>[...t[55]||(t[55]=[r("span",{class:"group-title"},"连接设置",-1)])]),default:a(()=>[l(p,{label:"连接超时"},{default:a(()=>[l(m,{modelValue:e.connectTimeout,"onUpdate:modelValue":t[26]||(t[26]=s=>e.connectTimeout=s),min:1,max:600,style:{width:"200px"}},null,8,["modelValue"]),t[56]||(t[56]=r("span",{style:{"margin-left":"8px"}},"秒",-1)),t[57]||(t[57]=r("div",{class:"form-tip"},"建立连接的超时时间",-1))]),_:1}),l(p,{label:"请求超时"},{default:a(()=>[l(m,{modelValue:e.timeout,"onUpdate:modelValue":t[27]||(t[27]=s=>e.timeout=s),min:1,max:600,style:{width:"200px"}},null,8,["modelValue"]),t[58]||(t[58]=r("span",{style:{"margin-left":"8px"}},"秒",-1)),t[59]||(t[59]=r("div",{class:"form-tip"},"HTTP/FTP 请求的超时时间",-1))]),_:1}),l(p,{label:"最大重试次数"},{default:a(()=>[l(m,{modelValue:e.maxTries,"onUpdate:modelValue":t[28]||(t[28]=s=>e.maxTries=s),min:0,max:100,style:{width:"200px"}},null,8,["modelValue"]),t[60]||(t[60]=r("div",{class:"form-tip"},"连接失败时的最大重试次数",-1))]),_:1}),l(p,{label:"重试等待时间"},{default:a(()=>[l(m,{modelValue:e.retryWait,"onUpdate:modelValue":t[29]||(t[29]=s=>e.retryWait=s),min:0,max:600,style:{width:"200px"}},null,8,["modelValue"]),t[61]||(t[61]=r("span",{style:{"margin-left":"8px"}},"秒",-1)),t[62]||(t[62]=r("div",{class:"form-tip"},"重试前的等待时间",-1))]),_:1}),l(p,{label:"最低下载速度"},{default:a(()=>[l(i,{modelValue:e.lowestSpeedLimit,"onUpdate:modelValue":t[30]||(t[30]=s=>e.lowestSpeedLimit=s),placeholder:"0 表示无限制",style:{width:"200px"}},null,8,["modelValue"]),t[63]||(t[63]=r("span",{style:{"margin-left":"8px"}},"B/s",-1)),t[64]||(t[64]=r("div",{class:"form-tip"},"最低下载速度限制，低于此速度将重连",-1))]),_:1}),l(p,{label:"分片大小"},{default:a(()=>[l(b,{modelValue:e.minSplitSize,"onUpdate:modelValue":t[31]||(t[31]=s=>e.minSplitSize=s),style:{width:"200px"}},{default:a(()=>[l(n,{label:"1M",value:"1M"}),l(n,{label:"5M",value:"5M"}),l(n,{label:"10M",value:"10M"}),l(n,{label:"20M",value:"20M"}),l(n,{label:"50M",value:"50M"}),l(n,{label:"100M",value:"100M"})]),_:1},8,["modelValue"]),t[65]||(t[65]=r("div",{class:"form-tip"},"文件分片的最小大小",-1))]),_:1}),l(p,{label:"最大连接数"},{default:a(()=>[l(m,{modelValue:e.maxConnectionPerServer,"onUpdate:modelValue":t[32]||(t[32]=s=>e.maxConnectionPerServer=s),min:1,max:16,style:{width:"200px"}},null,8,["modelValue"]),t[66]||(t[66]=r("div",{class:"form-tip"},"每个服务器的最大连接数",-1))]),_:1}),l(p,{label:"分片连接数"},{default:a(()=>[l(m,{modelValue:e.split,"onUpdate:modelValue":t[33]||(t[33]=s=>e.split=s),min:1,max:16,style:{width:"200px"}},null,8,["modelValue"]),t[67]||(t[67]=r("div",{class:"form-tip"},"每个文件的分片连接数",-1))]),_:1})]),_:1}),l(p,{style:{"margin-top":"24px"}},{default:a(()=>[l(K,null,{default:a(()=>[l(T,{type:"primary",onClick:C,disabled:!g(u).isConnected,loading:x.value},{default:a(()=>[...t[68]||(t[68]=[w(" 保存设置 ",-1)])]),_:1},8,["disabled","loading"]),l(T,{onClick:S,disabled:!g(u).isConnected},{default:a(()=>[...t[69]||(t[69]=[w(" 重新加载 ",-1)])]),_:1},8,["disabled"]),l(T,{onClick:k},{default:a(()=>[...t[70]||(t[70]=[w(" 重置为默认值 ",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[F,P.value]])])}}});const X=N(D,[["__scopeId","data-v-098296b1"]]);export{X as default};
