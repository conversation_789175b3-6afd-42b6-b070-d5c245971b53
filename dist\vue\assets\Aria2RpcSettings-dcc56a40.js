import{d as Q,x as G,K as J,r as c,a as X,c as Y,L as Z,g as D,l as s,y as $,f as m,k as t,v as y,e as r,B as h,o as u,m as l,p,q as b,Q as ee,R as te,S as le,E as v,i as se}from"./index-a49529e1.js";const oe={class:"rpc-settings"},ae={class:"connection-header"},ne={class:"test-info"},re={style:{color:"#f56c6c"}},ie=Q({__name:"Aria2RpcSettings",setup(ue){const C=G(),V=J(),f=c(),E=c(!1),S=c(!1),k=c(!1),a=X({protocol:"http",host:"localhost",port:6800,path:"/jsonrpc",secret:""}),U=c(1e4),A=c(!0),j=c(5e3),n=c(null),q={host:[{required:!0,message:"请输入主机地址",trigger:"blur"}],port:[{required:!0,message:"请输入端口号",trigger:"blur"},{type:"number",min:1,max:65535,message:"端口号必须在1-65535之间",trigger:"blur"}],path:[{required:!0,message:"请输入路径",trigger:"blur"}]},B=Y(()=>C.isConnecting?{type:"warning",text:"连接中..."}:C.isConnected?{type:"success",text:"已连接"}:{type:"danger",text:"未连接"});Z(async()=>{await V.initialize();const i=V.aria2Config;Object.assign(a,i)});async function N(){if(!f.value)return;try{await f.value.validate()}catch{return}S.value=!0,n.value=null;const i=Date.now();try{const g=await new le(a).getVersion(),_=Date.now()-i;n.value={success:!0,responseTime:_,version:g.version,features:g.enabledFeatures},v.success("连接测试成功")}catch(e){n.value={success:!1,error:e instanceof Error?e.message:"连接失败"},v.error("连接测试失败")}finally{S.value=!1}}async function z(){if(f.value)try{await f.value.validate(),await V.updateAria2Config(a),v.success("设置已保存")}catch(i){i!==!1&&v.error("保存设置失败")}}async function F(){if(f.value){try{await f.value.validate()}catch{return}k.value=!0;try{await V.updateAria2Config(a),await C.connect(a),v.success("连接成功")}catch(i){v.error("连接失败："+(i instanceof Error?i.message:"未知错误"))}finally{k.value=!1}}}function I(){const i=V.aria2Config;Object.assign(a,i),n.value=null,v.info("已重置为保存的设置")}return(i,e)=>{const g=r("el-tag"),_=r("el-option"),W=r("el-select"),d=r("el-form-item"),R=r("el-input"),T=r("el-input-number"),P=r("el-card"),H=r("el-switch"),x=r("el-button"),L=r("el-space"),M=r("el-form"),w=r("el-descriptions-item"),O=r("el-descriptions"),K=h("loading");return u(),D("div",oe,[e[25]||(e[25]=s("div",{class:"settings-header"},[s("h2",null,"RPC 连接设置"),s("p",{class:"settings-description"},"配置与 Aria2 服务器的连接参数")],-1)),$((u(),m(M,{ref_key:"formRef",ref:f,model:a,rules:q,"label-width":"150px",style:{"max-width":"600px"}},{default:t(()=>[l(P,{class:"setting-group"},{header:t(()=>[s("div",ae,[e[8]||(e[8]=s("span",{class:"group-title"},"连接配置",-1)),l(g,{type:B.value.type,size:"small"},{default:t(()=>[p(b(B.value.text),1)]),_:1},8,["type"])])]),default:t(()=>[l(d,{label:"协议",prop:"protocol"},{default:t(()=>[l(W,{modelValue:a.protocol,"onUpdate:modelValue":e[0]||(e[0]=o=>a.protocol=o),style:{width:"100%"}},{default:t(()=>[l(_,{label:"HTTP",value:"http"}),l(_,{label:"HTTPS",value:"https"}),l(_,{label:"WebSocket",value:"ws"}),l(_,{label:"WebSocket Secure",value:"wss"})]),_:1},8,["modelValue"]),e[9]||(e[9]=s("div",{class:"form-tip"},"推荐使用 WebSocket 以获得实时通知",-1))]),_:1}),l(d,{label:"主机地址",prop:"host"},{default:t(()=>[l(R,{modelValue:a.host,"onUpdate:modelValue":e[1]||(e[1]=o=>a.host=o),placeholder:"localhost 或 IP 地址"},null,8,["modelValue"]),e[10]||(e[10]=s("div",{class:"form-tip"},"Aria2 服务器的主机地址",-1))]),_:1}),l(d,{label:"端口",prop:"port"},{default:t(()=>[l(T,{modelValue:a.port,"onUpdate:modelValue":e[2]||(e[2]=o=>a.port=o),min:1,max:65535,style:{width:"100%"}},null,8,["modelValue"]),e[11]||(e[11]=s("div",{class:"form-tip"},"Aria2 RPC 服务端口（默认：6800）",-1))]),_:1}),l(d,{label:"路径",prop:"path"},{default:t(()=>[l(R,{modelValue:a.path,"onUpdate:modelValue":e[3]||(e[3]=o=>a.path=o),placeholder:"/jsonrpc"},null,8,["modelValue"]),e[12]||(e[12]=s("div",{class:"form-tip"},"RPC 接口路径（默认：/jsonrpc）",-1))]),_:1}),l(d,{label:"密钥",prop:"secret"},{default:t(()=>[l(R,{modelValue:a.secret,"onUpdate:modelValue":e[4]||(e[4]=o=>a.secret=o),type:"password",placeholder:"可选，RPC 访问密钥","show-password":"",clearable:""},null,8,["modelValue"]),e[13]||(e[13]=s("div",{class:"form-tip"},"如果 Aria2 设置了 RPC 密钥，请在此输入",-1))]),_:1})]),_:1}),l(P,{class:"setting-group"},{header:t(()=>[...e[14]||(e[14]=[s("span",{class:"group-title"},"连接选项",-1)])]),default:t(()=>[l(d,{label:"连接超时"},{default:t(()=>[l(T,{modelValue:U.value,"onUpdate:modelValue":e[5]||(e[5]=o=>U.value=o),min:1e3,max:3e4,step:1e3,style:{width:"200px"}},null,8,["modelValue"]),e[15]||(e[15]=s("span",{style:{"margin-left":"8px"}},"毫秒",-1)),e[16]||(e[16]=s("div",{class:"form-tip"},"连接超时时间（1000-30000毫秒）",-1))]),_:1}),l(d,{label:"自动重连"},{default:t(()=>[l(H,{modelValue:A.value,"onUpdate:modelValue":e[6]||(e[6]=o=>A.value=o)},null,8,["modelValue"]),e[17]||(e[17]=s("div",{class:"form-tip"},"连接断开时自动尝试重新连接",-1))]),_:1}),A.value?(u(),m(d,{key:0,label:"重连间隔"},{default:t(()=>[l(T,{modelValue:j.value,"onUpdate:modelValue":e[7]||(e[7]=o=>j.value=o),min:1e3,max:6e4,step:1e3,style:{width:"200px"}},null,8,["modelValue"]),e[18]||(e[18]=s("span",{style:{"margin-left":"8px"}},"毫秒",-1)),e[19]||(e[19]=s("div",{class:"form-tip"},"自动重连的时间间隔",-1))]),_:1})):y("",!0)]),_:1}),l(d,{style:{"margin-top":"24px"}},{default:t(()=>[l(L,null,{default:t(()=>[l(x,{type:"primary",onClick:N,loading:S.value},{default:t(()=>[...e[20]||(e[20]=[p(" 测试连接 ",-1)])]),_:1},8,["loading"]),l(x,{type:"success",onClick:F,loading:k.value},{default:t(()=>[...e[21]||(e[21]=[p(" 保存并连接 ",-1)])]),_:1},8,["loading"]),l(x,{onClick:z},{default:t(()=>[...e[22]||(e[22]=[p(" 仅保存 ",-1)])]),_:1}),l(x,{onClick:I},{default:t(()=>[...e[23]||(e[23]=[p(" 重置 ",-1)])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[K,E.value]]),n.value?(u(),m(P,{key:0,class:"test-result",style:{"margin-top":"20px","max-width":"600px"}},{header:t(()=>[...e[24]||(e[24]=[s("span",null,"连接测试结果",-1)])]),default:t(()=>[s("div",ne,[l(O,{column:2,border:""},{default:t(()=>[l(w,{label:"连接状态"},{default:t(()=>[l(g,{type:n.value.success?"success":"danger"},{default:t(()=>[p(b(n.value.success?"成功":"失败"),1)]),_:1},8,["type"])]),_:1}),n.value.success?(u(),m(w,{key:0,label:"响应时间"},{default:t(()=>[p(b(n.value.responseTime)+"ms ",1)]),_:1})):y("",!0),n.value.version?(u(),m(w,{key:1,label:"Aria2 版本"},{default:t(()=>[p(b(n.value.version),1)]),_:1})):y("",!0),n.value.features?(u(),m(w,{key:2,label:"支持功能"},{default:t(()=>[(u(!0),D(ee,null,te(n.value.features,o=>(u(),m(g,{key:o,size:"small",style:{margin:"2px"}},{default:t(()=>[p(b(o),1)]),_:2},1024))),128))]),_:1})):y("",!0),!n.value.success&&n.value.error?(u(),m(w,{key:3,label:"错误信息"},{default:t(()=>[s("span",re,b(n.value.error),1)]),_:1})):y("",!0)]),_:1})])]),_:1})):y("",!0)])}}});const pe=se(ie,[["__scopeId","data-v-18a5e704"]]);export{pe as default};
