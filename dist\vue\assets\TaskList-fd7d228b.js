import{r as L,a as Ke,c as U,_ as Ye,t as He,b as Z,d as _,e as w,o as Q,f as V,w as W,g as F,n as je,h as We,i as te,j as re,k as h,l as C,m as n,p as T,q as R,u as D,s as Pe,v as Y,x as Ge,y as qe,z as Oe,E as A,A as ie,B as ze,C as Xe,D as Ze,F as ne,G as _e,H as $e,I as et,J as tt}from"./index-a49529e1.js";import{g as st,f as at,a as ot,b as lt}from"./taskUtils-6207ebc5.js";const N=L(new Set),O=Ke(new Map);function ce(){const x=U(()=>N.value.size),s=U(()=>N.value.size>0),o=U(()=>Array.from(O.values())),I=U(()=>o.value.some(u=>u.status==="paused"||u.status==="waiting"||u.status==="error")),f=U(()=>o.value.some(u=>u.status==="active"));function r(u){N.value.add(u.gid),O.set(u.gid,{...u}),console.log("Task selected:",u.gid,"Total selected:",N.value.size)}function l(u){N.value.delete(u),O.delete(u),console.log("Task unselected:",u,"Total selected:",N.value.size)}function c(u){N.value.has(u.gid)?l(u.gid):r(u)}function p(){console.log("Clearing all selection"),N.value.clear(),O.clear()}function E(u){return N.value.has(u)}function y(u){u.forEach(S=>r(S))}function J(u){p(),y(u)}function B(u){N.value.has(u.gid)&&O.set(u.gid,{...u})}function k(u){u.forEach(S=>B(S))}function H(u){const S=new Set(u),b=[];N.value.forEach(v=>{S.has(v)||b.push(v)}),b.forEach(v=>l(v)),b.length>0&&console.log("Cleaned up non-existent selected tasks:",b)}return{selectedTaskGids:N.value,selectedTasks:o,selectedCount:x,hasSelection:s,canBatchStart:I,canBatchPause:f,selectTask:r,unselectTask:l,toggleTask:c,clearSelection:p,isTaskSelected:E,selectTasks:y,selectAll:J,updateSelectedTaskData:B,updateSelectedTasksData:k,cleanupNonExistentTasks:H}}class it{getTaskFilePaths(s){var r;const o=[];console.log(`Getting file paths for task ${s.gid}:`,{status:s.status,dir:s.dir,files:s.files,filesLength:((r=s.files)==null?void 0:r.length)||0}),s.files&&s.files.length>0&&s.files.forEach((l,c)=>{if(console.log(`  File ${c}:`,{path:l.path,selected:l.selected,length:l.length,completedLength:l.completedLength}),l.path&&l.path.trim()){let p=l.path.trim();const E=this.isAbsolutePath(p);if(console.log(`    Original file path: "${p}"`),console.log(`    Is absolute path: ${E}`),console.log(`    Task dir: "${s.dir}"`),!E&&s.dir){const J=p;p=this.joinPath(s.dir,p),console.log(`    Combined path: "${J}" + "${s.dir}" = "${p}"`)}else console.log(E?`    Using absolute path as-is: "${p}"`:`    No task.dir available, using path as-is: "${p}"`);const y=this.normalizePath(p);o.push(y),console.log(`    Final file path: "${y}"`)}});const I=o.filter(l=>!l.endsWith(".aria2")).map(l=>l+".aria2"),f=[...o,...I];return o.length===0&&s.dir&&console.log(`No files found in task.files, checking download directory: ${s.dir}`),console.log(`Final file paths for task ${s.gid} (including .aria2 files):`,f),f}isAbsolutePath(s){return/^([a-zA-Z]:[\\\/]|\\\\|\/)/i.test(s)}joinPath(s,o){const I=s.includes("\\")?"\\":"/";return s.endsWith(I)?s+o:s+I+o}normalizePath(s){const o=s.includes("\\")||/^[a-zA-Z]:/.test(s),I=o?"\\":"/",f=s.replace(/[\\\/]+/g,I);if(o){const r=f.match(/^([a-zA-Z]:[\\\/][^\\\/]+)[\\\/]\1(.*)$/i);if(r){const l=r[1],c=r[2],p=l+(c?I+c:"");return console.log(`    Detected duplicate path: "${f}" -> "${p}"`),p}}return f}async validateFilePath(s){var o;try{return(o=window.electronAPI)!=null&&o.openPath,!0}catch(I){return console.warn(`Failed to validate file path: ${s}`,I),!0}}getTaskDisplayName(s){var o,I;if((I=(o=s.bittorrent)==null?void 0:o.info)!=null&&I.name)return s.bittorrent.info.name;if(s.files&&s.files.length>0){const r=s.files[0].path;if(r)return r.split("/").pop()||r.split("\\").pop()||"Unknown"}return`Task ${s.gid}`}async deleteCompletedTask(s,o=!1){var r;const I=this.getTaskDisplayName(s),f={success:!1,taskId:s.gid,taskName:I,filesDeleted:0,errors:[]};console.log(`Deleting completed task ${s.gid} (${I}), deleteFiles: ${o}`);try{if(o&&((r=window.electronAPI)!=null&&r.deleteFiles)){const l=this.getTaskFilePaths(s);if(l.length>0){console.log(`Attempting to delete ${l.length} files for task ${s.gid}`);try{const c=await window.electronAPI.deleteFiles(l);if(console.log(`File deletion result for task ${s.gid}:`,c),c.success&&c.results){const p=c.results.filter(y=>y.success);f.filesDeleted=p.length;const E=c.results.filter(y=>!y.success);E.length>0&&E.forEach(y=>{f.errors.push(`删除文件失败: ${y.path} - ${y.error}`)})}else f.errors.push(`文件删除操作失败: ${c.error}`)}catch(c){const p=`删除文件时发生错误: ${c instanceof Error?c.message:String(c)}`;f.errors.push(p),console.error(p,c)}}else console.log(`Task ${s.gid} has no files to delete`)}try{try{const{useAria2Store:l}=await Ye(()=>import("./index-a49529e1.js").then(p=>p.a0),["./index-a49529e1.js","./index-47e19209.css"],import.meta.url),c=l();if(c.service&&c.isConnected){console.log(`Attempting to remove task ${s.gid} from Aria2`);try{await c.service.removeDownloadResult(s.gid),console.log(`Successfully removed task ${s.gid} from Aria2 download results`)}catch(p){console.log("Failed to remove from download results, trying regular remove:",p);try{await c.service.remove(s.gid),console.log(`Successfully removed task ${s.gid} from Aria2 (regular remove)`)}catch(E){console.log(`Failed to remove task ${s.gid} from Aria2 (may not exist):`,E)}}}else console.log(`Aria2 not connected, skipping Aria2 deletion for task ${s.gid}`)}catch(l){console.log(`Failed to remove task ${s.gid} from Aria2:`,l)}He.removePersistedTask(s.gid),console.log(`Removed task ${s.gid} from persistence storage`),Z.removeTaskTime(s.gid),console.log(`Removed task ${s.gid} time record`),f.success=!0,console.log(`Successfully deleted task record for ${s.gid}`)}catch(l){const c=`删除任务记录失败: ${l instanceof Error?l.message:String(l)}`;f.errors.push(c),console.error(c,l)}}catch(l){const c=`删除任务时发生未知错误: ${l instanceof Error?l.message:String(l)}`;f.errors.push(c),console.error(c,l)}return f}async batchDeleteCompletedTasks(s,o=!1){console.log(`Batch deleting ${s.length} completed tasks, deleteFiles: ${o}`);const I={totalTasks:s.length,successfulTasks:0,failedTasks:0,totalFilesDeleted:0,results:[],errors:[]};for(const f of s)try{const r=await this.deleteCompletedTask(f,o);I.results.push(r),r.success?(I.successfulTasks++,I.totalFilesDeleted+=r.filesDeleted):(I.failedTasks++,I.errors.push(...r.errors))}catch(r){I.failedTasks++;const l=`处理任务 ${f.gid} 时发生错误: ${r instanceof Error?r.message:String(r)}`;I.errors.push(l),console.error(l,r)}return console.log("Batch delete completed:",I),I}hasDeleteableFiles(s){return this.getTaskFilePaths(s).length>0}getTaskFilesSummary(s){const o=this.getTaskFilePaths(s);return{fileCount:o.length,filePaths:o}}}const z=new it,nt=_({__name:"TaskCheckbox",props:{task:{}},setup(x){const s=x,{isTaskSelected:o,toggleTask:I}=ce(),f=U(()=>o(s.task.gid));function r(l){console.log("Checkbox changed for task:",s.task.gid,"checked:",l),I(s.task)}return(l,c)=>{const p=w("el-checkbox");return Q(),V(p,{"model-value":f.value,onChange:r,onClick:c[0]||(c[0]=W(()=>{},["stop"]))},null,8,["model-value"])}}}),rt="data:image/png;base64,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",ct="data:image/png;base64,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",gt="data:image/png;base64,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",dt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAAAXNSR0IArs4c6QAADC9JREFUeF7tnY+RFTcMxk0lhEqASsh1sB0AHWwHgUrgKgmpJDkxfjMvj3uHLf+TrN/OMDeZ2LvWJ32WZMt+rxIPCIDAXQRegQ0IgMB9BCAI1gECLyAAQTAPEIAg2AAI6BDAg+hwo1cQBCBIEEUjpg4BCKLDjV5BEIAgQRSNmDoEIIgON3oFQQCCBFE0YuoQgCA63OgVBAEIEkTRiKlDAILocKNXEAQgSBBFI6YOAQiiw41eQRCAIEEUjZg6BCCIDjd6BUEAggRRNGLqEIAgOtzoFQQBCBJE0YipQwCC6HCjVxAEIEgQRSOmDgEIosONXkEQgCBBFI2YOgQgiA43egVBAIIEUTRi6hCAIDrc6BUEAQgSRNGIqUMAguhwo1cQBCBIEEUjpg4BCKLDjV5BEHBBkOM4/sj6uPy1rJ4fMrjzPH/+5fGNgEmCHMfxZ0rpbUrpXUrJAylesgIhyvW/R/lvCOSDOGYIchyHkOHDk/EIOSI831NKX58mge+Qxa66lxMkE+Nj9hZ2kRo7MiHL5/M85S+PIQSWEuQ4DiHGJ0N4rB4KRFmtgZvvLyFITrr/Cu41XjKFL9mjkOgvJsx0gmRy/L1Ybg+fF3K8Jz9Zq6qpBMn5xre1Irv7+qfzPD+7G/UmA55GEDxHk8U8nOcpYRfPZARmEkQ8hyzl8ugQgCQ63Jp6TSEIq1VNOrru/IacpBuWRS8aThBCqyI9lDYicS9FqlO7GQRp2esQg5C9ASnPcLPjnCeF6/qx11dlM62lMyTtnYy/5DVDCdLoPbaMuTtVDsjyL7vuJRbe2GY0QTTeQxQv5Nh6kywTRTZLNR5FvOn7Rt3TvQCB0QT5t2AM102kyvVNZR/XzY/jEJJoCjRJ2CdofhhBlOFVyNBBucqHF3FOkNrwasuco1SHSpLgRUoBVrYb6UGqNgafyimGjUWJzfRux3FIjVpNTsKK1mAtDTPK4zhqCEK4kFJS1KrJQob1Oq3LYovLo8gjCVKToDMT5pmwIWkfPJd2e72sUv48TelhpdIKQUIm58+ZXD6PLytbER7zB8SsEIRk84oOilzEO5nMEgWCGDStAGHWPdTl+LWp0AuC2CSIbBxGCbNuNWCqkgKC2CSILPVGPpZspmoZghgkiAwpYB5yqwkTF1dAELsEqa1EMCpJ07CWL/9DkCb9je2MF/mJ79ItAAgy1sab3p4LPqUioab8pOmbBjsvzUcgiEGLuB1S3jyUe4s9Pj1u5l8WakEQjybncMwdTlIu2UyGIA6NzfOQszeUBYjasHHJcQgI4tnanI5dedx4ScU3BHFqZN6HrSjtF5Gnh1kQxLulOR6/ouYMgjjWN0OvREBxb8H0PAQPUqlUmvdFoHIz9Mt5ng99R/Dy2yDITLT51i8IVB7NhiDYUCwEKvMQCBLLPJC28rojCILJxEIAgpTpe/ryXdmwaDUaAQhShjAEKcNpu1YQpEylQwmS63/e5vqfHtWlZVLRqjcC08+rb7vMm1dH5DcRa4vieiuV9/VHYNrlc1sRJNf3yLkJzc8J9Fcjb5yBwNCz69sQpHLDaYbi+MZcBIaUobgnSLCrOueanL+vdb94zjVB8Br+LHjCiLsm8m4JAjkmmJrfT3S76MEdQXKJtFzLKStUPCBwD4EuFz14JEjND/NgPrERaCaJK4JU7rrGNg2kvyDQtAnthiDKM8yYCQg0eREXBFEczXzJLGRjSZ5HbMc0Aq/z6C7VEC0VEWov4oUgrRc5Cynkh1lkCZDHIQKNVRJqL2KeII3eo+uauEO72m7IyjxUfaeWB4JovYd61tjOqjYTSEkSVZjlgSA1Pyd9MYUhdTmb2ZlrcRQbxSqbME0Q5UyB53Bt+mWDV9Tgqc6zWyeI7JjXlK7/OM/zTRnEtPKOwIw7tcwSRJmcL/01Iu8G5238lVcGqRJ1ywSR9W8pKyl9CK1KkdqkXWUeooouLBOkdvVKlYRtYishxYhOEPmd8OLd0/M8h5E9pPU5ELqSIPuEWIr8QyW8AxtgiC8gUEmQfVaxFMu75B/BqKSYRFU2MiwseTLymg2+/+1yVs4MYhqqXdJgNrWVuIrq7q0IUkMu1erEVtYSUBhFlKHaAjDnQWa5zoA2tZXIlRGKehK1SBCWd7cy5f7CKCZRVYIuI7dIkKoz5yzv9jdA629UhFeq/MMcQRQzg9p1WjcCxncfgdpFnJZJ1JQHmTkzYIB+EZiVf1j0ILX5h2plwq9pMHJFlKEOrywShOVdOPAiAoooo2kSNRNiZVSk/qr0Ua9MlH6AdvYQmJl/mPIgKSX5XQ+5nbv0aXKdpR+hnR0EFOFV8yKOJQ9Sdd9uy8qEHZUzkhoEZpWXXI/JCkHeVx6Oap4ZahRDWxsIzM4/LIVYDykl8SClD+FVKVIbtas8g95lErXiQeTmw5rLGZpWJjaymTCiKPKPLmeErBBEfvCE04NhzL1eUEV41SXKsEKQGsS6zAw1H6TtegQqbzCRAXc5I+SRIF1mhvUqZwQ1CMwsL7G4ilWDVZeZoeaDtF2LgCL/6DaJevMgXVYm1qqbr9cioMg/ul0B5Y0g3WaGWiXRfh0Cs8tLPIdYEGSdnS75siK86hpluPIglJcssdGlH11RXuLVg3SdGZZqnY8XI6DIP7puInvyIIRXxWa1T8MV5SVePUjXmWEfE9pXEkX+0X0T2YsHIbzalwd3JVOEV92jDC8E4fQgBClBoPsmsheCdJ8ZStCmzVoEVpWXuMtBWN5da6grvq7IP4ZMoh48CPnHCgtd/E0L+YdA4IEgQ2aGxfrn879BYGV5ibcQi+XdYHRShFfDogzzHoT8Ixg7Ukqry0s8eZBhM0M8s/MjsSL/GBZlWPcg5B9+7LrbSCvLS9LIKMM6Qbpv/HTTIi8agoAi/+heXuIlxCK8GmKCtl+qCK+GRhmWPchQwW2bSdzRKQgyLP+wvg8CQQLyxEJ5iYsQa2TiFdDuXIisyD+GT6JWQyzyDxcm3XeQivAqLEGGC95XtbytBwJWyks8hFhDE68eyuQdfRFQhFdTogyTIRb5R1/j8/A2S+Ul1j3IlJnBg9FEGqMi/5gSZVj0IByvjcSMLKul8hLrHoQEPRhBrOYfVjcKu108HMzO3Ip7HIf8upjJn+AbGWLJb54X/2rUlXYhiFtT1w3cav4x2oN8Sym9U0AGQRSgee1iObwaTRBxmTU/zHnRMQTxau2KcSu8x9QcdWSIVRtXQhCFgXnvUrt69STvNgSR8ErCrNqHZd5axJy2V3iPoacHn4NxmAeRjylmB+nGRqFTg68ddmVp+xLbGE0QbR7CUdtaa3PWXvGzziLh1PBqaJKePYg2zJoOhDP7cj1cxb7HEu8xnCANYZZ0xYu4psHzg1cUJV5etGTSHBpiZYJ8FNeo0PWPlJIUpMlfng0QaCDHsrx0OEEavciXp/uDP0MS/+xQhlVLvceUEKvRi0j37ykl2TzEkzjkSd4pl8UaTVXFstzjAvUUD9LoRS5jfRCyQBQ/LGn0GhdBp5z7uIfqTIJoV7Ruxy75zON5nuJZeIwhkD3GB2Xe+Yuun06Xfl4p4jSCdAi1bnGSkOv63z8rgQz+7de5clsmQU0F93PwDb1StFRfUwmSSaKt8i2ViXb+EVi2anUL3XSCdMpH/JsAEtxDwAw5ZICrCCJuWDxJL3eMue2BgClyLCNI9iKQZA+j7iWFOXIsJcgFVWXRWi+l8B4bCJg94rAkxLrVST4XIAesCLlsGOzMUSzd5/idoCYIkkMuWSKUHVdI8jut7fH/XVRImCHIVcglnkQKHCHKHkS4lUKIIfV1LjZ6zREEouzJilxT54YYFy2YJcgVUST0ktKFnru021qhMcGkGluqHb56raEzT5BrhefzBBJ6vb0KwS6hGCHZWnZIyHSpuH7cpbDUFUHW6p+vR0QAgkTUOjIXIwBBiqGiYUQEIEhErSNzMQIQpBgqGkZEAIJE1DoyFyMAQYqhomFEBCBIRK0jczECEKQYKhpGRACCRNQ6MhcjAEGKoaJhRAQgSEStI3MxAhCkGCoaRkQAgkTUOjIXIwBBiqGiYUQEIEhErSNzMQIQpBgqGkZEAIJE1DoyFyMAQYqhomFEBCBIRK0jczECEKQYKhpGRACCRNQ6MhcjAEGKoaJhRAQgSEStI3MxAhCkGCoaRkTgP/jBthQElAFdAAAAAElFTkSuQmCC",ut="data:image/png;base64,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",At="data:image/png;base64,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",pt=["src","alt"],ht=_({__name:"CustomIcon",props:{name:{},size:{default:"medium"},color:{default:"#909399"}},setup(x){const s=x,o={start:"开始.png",pause:"暂停.png",delete:"删除.png",download:"下载.png",open:"打开.png",detail:"详情.png"},I=U(()=>{const r=o[s.name];return new URL(Object.assign({"../icon/下载.png":rt,"../icon/删除.png":ct,"../icon/开始.png":gt,"../icon/打开.png":dt,"../icon/暂停.png":ut,"../icon/详情.png":At})[`../icon/${r}`],self.location).href}),f=U(()=>s.color==="#909399"?"brightness(0) saturate(100%) invert(64%) sepia(8%) saturate(255%) hue-rotate(202deg) brightness(95%) contrast(89%)":s.color==="#409eff"?"brightness(0) saturate(100%) invert(58%) sepia(96%) saturate(1458%) hue-rotate(204deg) brightness(97%) contrast(100%)":s.color==="#67c23a"?"brightness(0) saturate(100%) invert(64%) sepia(88%) saturate(394%) hue-rotate(76deg) brightness(94%) contrast(86%)":s.color==="#e6a23c"?"brightness(0) saturate(100%) invert(71%) sepia(77%) saturate(1284%) hue-rotate(12deg) brightness(94%) contrast(89%)":s.color==="#f56c6c"?"brightness(0) saturate(100%) invert(58%) sepia(89%) saturate(2142%) hue-rotate(329deg) brightness(102%) contrast(94%)":"none");return(r,l)=>(Q(),F("img",{src:I.value,alt:r.name,class:je(["custom-icon",`custom-icon-${r.size}`]),style:We({filter:f.value})},null,14,pt))}});const G=te(ht,[["__scopeId","data-v-f9d593ad"]]),It={class:"delete-dialog-content"},ft={class:"warning-icon"},mt={class:"delete-message"},Et={key:0,class:"task-name"},kt={key:1,class:"task-count"},Qt={key:0,class:"file-delete-option"},Ct={class:"option-section"},Bt={style:{"margin-top":"16px"}},St={class:"dialog-footer"},vt=_({__name:"DeleteTaskDialog",props:{modelValue:{type:Boolean},tasks:{},taskName:{},taskType:{}},emits:["update:modelValue","confirm"],setup(x,{emit:s}){const o=x,I=s,f=U({get:()=>o.modelValue,set:B=>I("update:modelValue",B)}),r=L(!1),l=L(!1),c=U(()=>o.tasks.length),p=U(()=>{var B;return!!((B=window.electronAPI)!=null&&B.deleteFiles)&&E.value.length>0}),E=U(()=>{const B=[];return console.log("DeleteTaskDialog - Analyzing tasks for file deletion:",{taskType:o.taskType,taskCount:o.tasks.length,tasks:o.tasks}),o.tasks.forEach((k,H)=>{var u;if(console.log(`Task ${H} (${k.gid}):`,{status:k.status,files:k.files,filesLength:((u=k.files)==null?void 0:u.length)||0,dir:k.dir}),o.taskType==="stopped"){const S=z.getTaskFilePaths(k);B.push(...S),console.log(`  Completed task ${k.gid} files:`,S)}else if(k.files&&k.files.length>0){const S=[];k.files.forEach((v,j)=>{console.log(`  File ${j}:`,{path:v.path,selected:v.selected,length:v.length,completedLength:v.completedLength}),v.path&&v.path.trim()&&S.push(v.path)});const b=S.filter(v=>!v.endsWith(".aria2")).map(v=>v+".aria2");B.push(...S,...b),console.log(`  Task ${k.gid} files (including .aria2):`,[...S,...b])}else console.log(`  Task ${k.gid} has no files or empty files array`)}),console.log("DeleteTaskDialog - Final file list:",B),B});re(f,B=>{B&&(l.value=!1,r.value=!1)});function y(){r.value||(f.value=!1)}async function J(){try{r.value=!0,I("confirm",l.value)}finally{r.value=!1}}return(B,k)=>{const H=w("el-icon"),u=w("el-divider"),S=w("el-checkbox"),b=w("el-button"),v=w("el-dialog");return Q(),V(v,{modelValue:f.value,"onUpdate:modelValue":k[1]||(k[1]=j=>f.value=j),title:"删除任务",width:"500px","before-close":y},{footer:h(()=>[C("div",St,[n(b,{onClick:y},{default:h(()=>[...k[3]||(k[3]=[T("取消",-1)])]),_:1}),n(b,{type:"danger",onClick:J,loading:r.value},{default:h(()=>[T(R(l.value?"删除任务和文件":"删除任务"),1)]),_:1},8,["loading"])])]),default:h(()=>[C("div",It,[C("div",ft,[n(H,{size:"48",color:"#f56c6c"},{default:h(()=>[n(D(Pe))]),_:1})]),C("div",mt,[C("h3",null,"确定要删除"+R(c.value>1?`这 ${c.value} 个`:"这个")+"任务吗？",1),c.value===1&&B.taskName?(Q(),F("p",Et,R(B.taskName),1)):c.value>1?(Q(),F("p",kt,"已选择 "+R(c.value)+" 个任务",1)):Y("",!0)]),p.value?(Q(),F("div",Qt,[n(u),C("div",Ct,[C("div",Bt,[n(S,{modelValue:l.value,"onUpdate:modelValue":k[0]||(k[0]=j=>l.value=j),disabled:E.value.length===0},{default:h(()=>[...k[2]||(k[2]=[T(" 同时删除文件 ",-1)])]),_:1},8,["modelValue","disabled"])])])])):Y("",!0)])]),_:1},8,["modelValue"])}}});const Tt=te(vt,[["__scopeId","data-v-d9346f8c"]]),yt={class:"task-list"},wt={class:"task-list-header"},Dt={class:"task-stats"},Rt={key:0},Ut={class:"task-actions"},Ft={class:"action-left"},Mt={class:"batch-actions"},Jt={class:"action-buttons-group"},bt={key:0,class:"selected-count"},Nt={class:"action-right"},Vt={class:"search-box"},Lt={class:"task-list-content"},xt={class:"file-info"},Kt={class:"file-name"},Yt={key:0,class:"file-path"},Ht={class:"status-column"},jt={class:"action-buttons"},Wt=["onClick","disabled"],Pt={key:0,class:"loading-spinner"},Gt=["onClick","disabled"],qt={key:0,class:"loading-spinner"},Ot=["onClick","disabled"],zt={key:0,class:"loading-spinner"},Xt=["onClick"],Zt=["onClick"],_t=["onClick"],$t={style:{"margin-top":"16px"}},es=_({__name:"TaskList",props:{taskType:{}},setup(x){const s=x,o=Ge(),I=Oe(),f=L(!1),r=L(""),l=L(!1),c=L(!1),p=L([]),E=L(new Set),y=L(),{selectedTasks:J,selectedCount:B,hasSelection:k,canBatchStart:H,canBatchPause:u,clearSelection:S,selectAll:b,toggleTask:v,updateSelectedTasksData:j,cleanupNonExistentTasks:ge}=ce(),de=L(!1),X=L(""),ue=U(()=>{switch(s.taskType){case"active":return"正在下载";case"waiting":return"等待中";case"stopped":return"下载完成";case"active-and-waiting":return"下载任务";default:return"任务列表"}}),$=U(()=>{let t=[];switch(s.taskType){case"active":t=[...o.activeTasks];break;case"waiting":t=[...o.waitingTasks];break;case"stopped":t=[...o.stoppedTasks];break;case"active-and-waiting":t=[...o.activeTasks,...o.waitingTasks];break;default:return[]}return t.sort((e,a)=>{const g={error:4,active:3,waiting:2,paused:1},d=g[e.status]||0,m=g[a.status]||0;return d!==m?m-d:parseInt(a.gid,16)-parseInt(e.gid,16)})}),M=U(()=>{let t=[...$.value];if(X.value.trim()){const e=X.value.toLowerCase().trim();t=t.filter(a=>!!(ae(a).toLowerCase().includes(e)||a.files&&a.files.length>0&&a.files.some(m=>m.path&&m.path.toLowerCase().includes(e))||a.files&&a.files.length>0&&a.files.some(m=>m.uris&&m.uris.some(P=>P.uri&&P.uri.toLowerCase().includes(e)))||a.gid.toLowerCase().includes(e)))}return s.taskType==="stopped"?t.sort((e,a)=>{const g=Z.getCompleteTime(e.gid),d=Z.getCompleteTime(a.gid);return g&&d?d-g:g&&!d?-1:!g&&d?1:parseInt(a.gid,16)-parseInt(e.gid,16)}):t.sort((e,a)=>{const g={error:4,active:3,waiting:2,paused:1},d=g[e.status]||0,m=g[a.status]||0;return d!==m?m-d:parseInt(a.gid,16)-parseInt(e.gid,16)})}),se=U(()=>st(M.value));function ae(t){return t.files&&t.files.length>0?t.files[0].path.split("/").pop()||t.files[0].path:t.gid}function Ae(t){const e=parseInt(t);if(e===0)return"0 B";const a=1024,g=["B","KB","MB","GB","TB"],d=Math.floor(Math.log(e)/Math.log(a));return parseFloat((e/Math.pow(a,d)).toFixed(1))+" "+g[d]}function oe(t){return at(parseInt(t))}function pe(t){const e=ot(t);return lt(e)}function he(t){var e,a;if((a=(e=t.bittorrent)==null?void 0:e.info)!=null&&a.name)return t.bittorrent.info.name;if(t.files&&t.files.length>0){const d=t.files[0].path;return d.split("/").pop()||d.split("\\").pop()||"Unknown"}return`Task ${t.gid}`}function Ie(t){const e=Z.getCompleteTime(t.gid);if(e){const a=new Date(e),g=new Date,d=new Date(g.getFullYear(),g.getMonth(),g.getDate()),m=new Date(a.getFullYear(),a.getMonth(),a.getDate());if(m.getTime()===d.getTime())return a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"});const P=new Date(d.getTime()-24*60*60*1e3);return m.getTime()===P.getTime()?"昨天 "+a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):a.getFullYear()===g.getFullYear()?a.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})+" "+a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}):a.toLocaleDateString("zh-CN")+" "+a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})}return"--"}function fe(t){const e=parseInt(t.totalLength),a=parseInt(t.completedLength);return e===0?0:Math.round(a/e*100)}function me(t){switch(t.status){case"complete":return"success";case"error":return"exception";case"active":return"";default:return""}}function Ee(t){switch(t){case"active":return"primary";case"waiting":return"warning";case"paused":return"info";case"complete":return"success";case"error":return"danger";default:return""}}function ke(t){switch(t){case"active":return"下载中";case"waiting":return"等待中";case"paused":return"已暂停";case"complete":return"已完成";case"error":return"错误";case"removed":return"已删除";default:return t}}async function Qe(t){if(E.value.has(t)){console.log("Task is already being operated:",t);return}E.value.add(t);try{console.log("Pausing task:",t);const e=M.value.find(a=>a.gid===t);e&&(e.status="paused",e.downloadSpeed="0"),await o.pauseTask(t,!0),A.success("任务已暂停"),setTimeout(async()=>{await o.loadAllTasks()},1e3)}catch(e){console.error("暂停任务失败:",e),A.error(`暂停任务失败: ${e.message||e}`),await o.loadAllTasks()}finally{E.value.delete(t)}}async function Ce(t){if(E.value.has(t)){console.log("Task is already being operated:",t);return}E.value.add(t);try{console.log("Starting task:",t);const e=M.value.find(a=>a.gid===t);e&&(e.status="active"),await o.unpauseTask(t),A.success("任务已开始"),setTimeout(async()=>{await o.loadAllTasks()},1e3)}catch(e){console.error("开始任务失败:",e),A.error(`开始任务失败: ${e.message||e}`),await o.loadAllTasks()}finally{E.value.delete(t)}}async function Be(t){if(E.value.has(t)){console.log("Task is already being operated:",t);return}E.value.add(t);try{console.log("Retrying task:",t);const e=M.value.find(g=>g.gid===t);e&&(e.status="active");const a=await o.retryErrorTask(t);A.success("任务重试成功"),console.log("Task retried with new GID:",a),await o.loadAllTasks()}catch(e){console.error("重试任务失败:",e),A.error(`重试任务失败: ${e.message||e}`),await o.loadAllTasks()}finally{E.value.delete(t)}}async function Se(t){var e;try{const a=M.value.find(m=>m.gid===t);if(!a){A.error("任务不存在");return}console.log(`Removing single task ${t}, taskType: ${s.taskType}`);const g=!!((e=window.electronAPI)!=null&&e.deleteFiles);let d=!1;if(s.taskType==="stopped"?(d=z.hasDeleteableFiles(a),console.log(`Completed task ${t} has deleteable files: ${d}`)):(d=a.files&&a.files.length>0&&a.files.some(m=>m.path&&m.path.trim()),console.log(`Active/waiting task ${t} has files: ${d}`)),g&&d)p.value=[a],c.value=!0;else if(await ie.confirm("确定要删除这个任务吗？","删除任务",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}),s.taskType==="stopped"){const m=await z.deleteCompletedTask(a,!1);await o.loadAllTasks(),m.success?A.success("任务已删除"):A.error(`删除失败: ${m.errors.join(", ")}`)}else await o.removeTask(t,!1,!1),A.success("任务已删除")}catch(a){a!=="cancel"&&(console.error("删除任务失败:",a),A.error("删除任务失败"))}}function ve(t){v(t)}function Te(t){I.push(`/task/${t}`)}async function ye(t){if(!window.electronAPI){A.warning("此功能仅在桌面版中可用");return}if(!t.dir){A.warning("没有找到文件目录信息");return}try{let e;if(window.electronAPI.openInExplorer){if(console.log("Using Windows Explorer method for task:",t.gid),t.files&&t.files.length>0){const a=t.files[0];if(a.path&&(e=await window.electronAPI.openInExplorer(a.path),e!=null&&e.success)){A.success("已打开文件位置");return}}if(e=await window.electronAPI.openInExplorer(t.dir),e!=null&&e.success){A.success("已打开目录");return}}if(console.log("Fallback to showItemInFolder method for task:",t.gid),t.files&&t.files.length>0){const a=t.files[0];if(a.path&&(e=await window.electronAPI.showItemInFolder(a.path),e!=null&&e.success)){A.success("已打开文件位置");return}}e=await window.electronAPI.openPath(t.dir),e!=null&&e.success?A.success("已打开目录"):A.error(`打开目录失败: ${(e==null?void 0:e.error)||"未知错误"}`)}catch(e){console.error("Failed to open task location:",e),A.error("打开位置失败")}}function ee(){f.value=!1,r.value="",l.value=!1}async function we(){try{console.log("Deleting task:",r.value,"deleteFiles:",l.value),await o.removeTask(r.value,!1,l.value),A.success(l.value?"任务和文件已删除":"任务已删除"),ee()}catch(t){console.error("删除任务失败:",t);let e="删除任务失败";if(t instanceof Error)if(t.message.includes("not found")){e="任务不存在，可能已被删除",A.warning(e),ee(),await o.loadAllTasks();return}else e=t.message;A.error(e)}}async function De(){try{const t=J.value.filter(e=>e.status==="paused"||e.status==="waiting"||e.status==="error");if(t.length===0){A.warning("没有可以开始的任务");return}for(const e of t)e.status==="error"?await o.retryErrorTask(e.gid):await o.unpauseTask(e.gid);await o.loadAllTasks(),A.success(`已开始 ${t.length} 个任务`),S()}catch(t){console.error("开始任务失败:",t),A.error("开始任务失败")}}async function Re(){try{const t=J.value.filter(e=>e.status==="active"||e.status==="waiting");if(t.length===0){A.warning("没有可以暂停的任务");return}for(const e of t)await o.pauseTask(e.gid,!0);A.success(`已暂停 ${t.length} 个任务`),S()}catch(t){console.error("暂停任务失败:",t),A.error("暂停任务失败")}}async function Ue(){var t;try{if(B.value===0){A.warning("请先选择要删除的任务");return}const e=!!((t=window.electronAPI)!=null&&t.deleteFiles);let a=!1;s.taskType==="stopped"?(a=J.value.some(g=>z.hasDeleteableFiles(g)),console.log(`Completed tasks have deleteable files: ${a}`)):(a=J.value.some(g=>g.files&&g.files.length>0&&g.files.some(d=>d.path&&d.path.trim())),console.log(`Active/waiting tasks have files: ${a}`)),e&&a?(p.value=[...J.value],c.value=!0):(await ie.confirm(`确定要删除选中的 ${B.value} 个任务吗？`,"删除任务",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}),await le(!1))}catch(e){e!=="cancel"&&(console.error("删除任务失败:",e),A.error("删除任务失败"))}}async function le(t){try{c.value=!1;const e=p.value.length>0?p.value:[...J.value];if(console.log(`Handling batch delete for ${e.length} tasks, deleteFiles: ${t}`),console.log("Task type:",s.taskType),s.taskType==="stopped"){console.log("Using completed task delete service");const a=await z.batchDeleteCompletedTasks(e,t);if(await o.loadAllTasks(),a.successfulTasks===a.totalTasks){let g=`已删除 ${a.successfulTasks} 个任务`;t&&a.totalFilesDeleted>0&&(g+=`和 ${a.totalFilesDeleted} 个文件`),A.success(g)}else A.warning(`已删除 ${a.successfulTasks}/${a.totalTasks} 个任务`),a.errors.length>0&&(console.error("Delete errors:",a.errors),a.errors.slice(0,3).forEach(g=>{A.error(g)}))}else{console.log("Using aria2Store.removeTask for active/waiting tasks");let a=0;for(const g of e)try{await o.removeTask(g.gid,!1,t),a++}catch(d){console.error(`Failed to delete task ${g.gid}:`,d)}if(a===e.length){const g=t?`已删除 ${a} 个任务和相关文件`:`已删除 ${a} 个任务`;A.success(g)}else A.warning(`已删除 ${a}/${e.length} 个任务`)}S(),p.value=[]}catch(e){console.error("删除任务失败:",e),A.error("删除任务失败")}}function Fe(){if(M.value.length===0){A.warning("没有可选择的任务");return}b(M.value),A.success(`已选择 ${M.value.length} 个任务`)}function Me(t){console.log("Search term:",t)}return re(()=>M.value,t=>{j(t);const e=t.map(a=>a.gid);ge(e)},{deep:!0}),(t,e)=>{const a=w("el-tag"),g=w("el-space"),d=w("el-icon"),m=w("el-button"),P=w("el-divider"),Je=w("el-input"),K=w("el-table-column"),be=w("el-progress"),Ne=w("el-table"),Ve=w("el-checkbox"),Le=w("el-dialog"),xe=ze("loading");return Q(),F("div",yt,[C("div",wt,[C("h2",null,R(ue.value),1),C("div",Dt,[n(g,null,{default:h(()=>[C("span",null,"共 "+R($.value.length)+" 个任务",1),M.value.length!==$.value.length?(Q(),F("span",Rt," (显示 "+R(M.value.length)+" 个) ",1)):Y("",!0),se.value.totalSpeed>0?(Q(),V(a,{key:1,type:"primary",size:"small"},{default:h(()=>[T(" 总速度: "+R(oe(se.value.totalSpeed)),1)]),_:1})):Y("",!0)]),_:1})])]),C("div",Ut,[C("div",Ft,[n(m,{size:"default",type:"primary",onClick:e[0]||(e[0]=i=>t.$router.push("/new"))},{default:h(()=>[n(d,null,{default:h(()=>[n(D(Xe))]),_:1}),e[6]||(e[6]=T(" 新建下载 ",-1))]),_:1}),n(P,{direction:"vertical",class:"action-divider"}),C("div",Mt,[C("div",Jt,[n(m,{size:"default",onClick:Fe,disabled:M.value.length===0},{default:h(()=>[n(d,null,{default:h(()=>[n(D(Ze))]),_:1}),e[7]||(e[7]=T(" 全选 ",-1))]),_:1},8,["disabled"]),n(m,{size:"default",type:"primary",onClick:De,disabled:!D(H)},{default:h(()=>[n(d,null,{default:h(()=>[n(D(ne))]),_:1}),e[8]||(e[8]=T(" 开始 ",-1))]),_:1},8,["disabled"]),n(m,{size:"default",type:"warning",onClick:Re,disabled:!D(u)},{default:h(()=>[n(d,null,{default:h(()=>[n(D(_e))]),_:1}),e[9]||(e[9]=T(" 暂停 ",-1))]),_:1},8,["disabled"]),n(m,{size:"default",type:"danger",onClick:Ue,disabled:!D(k)},{default:h(()=>[n(d,null,{default:h(()=>[n(D($e))]),_:1}),e[10]||(e[10]=T(" 删除 ",-1))]),_:1},8,["disabled"]),D(k)?(Q(),V(m,{key:0,size:"default",onClick:D(S)},{default:h(()=>[...e[11]||(e[11]=[T(" 取消选择 ",-1)])]),_:1},8,["onClick"])):Y("",!0)]),D(k)?(Q(),F("span",bt,"已选择 "+R(D(B))+" 个任务",1)):Y("",!0)])]),C("div",Nt,[C("div",Vt,[n(Je,{modelValue:X.value,"onUpdate:modelValue":e[1]||(e[1]=i=>X.value=i),placeholder:"搜索任务...","prefix-icon":D(et),clearable:"",onInput:Me,style:{width:"200px"}},null,8,["modelValue","prefix-icon"])])])]),C("div",Lt,[qe((Q(),V(Ne,{ref_key:"tableRef",ref:y,data:M.value,style:{width:"100%"},onRowClick:ve,"empty-text":"没有找到匹配的任务",class:"task-table","row-key":"gid"},{default:h(()=>[n(K,{width:"55",fixed:"left",label:""},{default:h(({row:i})=>[n(nt,{task:i},null,8,["task"])]),_:1}),n(K,{prop:"gid",label:"GID",width:"120"}),n(K,{label:"文件名","min-width":"200"},{default:h(({row:i})=>[C("div",xt,[C("div",Kt,R(ae(i)),1),i.dir?(Q(),F("div",Yt,R(i.dir),1)):Y("",!0)])]),_:1}),n(K,{label:"大小",width:"100"},{default:h(({row:i})=>[T(R(Ae(i.totalLength)),1)]),_:1}),n(K,{label:"进度",width:"120"},{default:h(({row:i})=>[n(be,{percentage:fe(i),status:me(i),"stroke-width":6},null,8,["percentage","status"])]),_:1}),n(K,{label:"状态",width:"120"},{default:h(({row:i})=>[C("div",Ht,[n(a,{type:Ee(i.status),size:"small"},{default:h(()=>[T(R(ke(i.status)),1)]),_:2},1032,["type"]),i.status==="active"?(Q(),V(d,{key:0,class:"status-icon active"},{default:h(()=>[n(D(ne))]),_:1})):i.status==="waiting"?(Q(),V(d,{key:1,class:"status-icon waiting"},{default:h(()=>[n(D(tt))]),_:1})):Y("",!0)])]),_:1}),n(K,{label:"下载速度",width:"120"},{default:h(({row:i})=>[T(R(oe(i.downloadSpeed)),1)]),_:1}),t.taskType==="stopped"?(Q(),V(K,{key:0,label:"完成时间",width:"150"},{default:h(({row:i})=>[T(R(Ie(i)),1)]),_:1})):(Q(),V(K,{key:1,label:"剩余时间",width:"120"},{default:h(({row:i})=>[T(R(pe(i)),1)]),_:1})),n(K,{label:"操作",width:"200",fixed:"right",align:"center","header-align":"center"},{default:h(({row:i})=>[C("div",jt,[i.status==="paused"?(Q(),F("button",{key:0,onClick:W(q=>Ce(i.gid),["stop"]),title:"开始下载",class:"task-action-btn",disabled:E.value.has(i.gid)},[E.value.has(i.gid)?(Q(),F("div",Pt)):(Q(),V(G,{key:1,name:"start",size:"medium"}))],8,Wt)):i.status==="error"?(Q(),F("button",{key:1,onClick:W(q=>Be(i.gid),["stop"]),title:"重试下载",class:"task-action-btn",disabled:E.value.has(i.gid)},[E.value.has(i.gid)?(Q(),F("div",qt)):(Q(),V(G,{key:1,name:"start",size:"medium"}))],8,Gt)):i.status==="active"?(Q(),F("button",{key:2,onClick:W(q=>Qe(i.gid),["stop"]),title:"暂停下载",class:"task-action-btn",disabled:E.value.has(i.gid)},[E.value.has(i.gid)?(Q(),F("div",zt)):(Q(),V(G,{key:1,name:"pause",size:"medium"}))],8,Ot)):Y("",!0),t.taskType==="stopped"?(Q(),F("button",{key:3,onClick:W(q=>ye(i),["stop"]),title:"打开位置",class:"task-action-btn"},[n(G,{name:"open",size:"medium"})],8,Xt)):Y("",!0),C("button",{onClick:W(q=>Se(i.gid),["stop"]),title:"删除任务",class:"task-action-btn"},[n(G,{name:"delete",size:"medium"})],8,Zt),C("button",{onClick:W(q=>Te(i.gid),["stop"]),title:"查看详情",class:"task-action-btn"},[n(G,{name:"detail",size:"medium"})],8,_t)])]),_:1})]),_:1},8,["data"])),[[xe,de.value]])]),n(Le,{modelValue:f.value,"onUpdate:modelValue":e[4]||(e[4]=i=>f.value=i),title:"删除任务",width:"400px","before-close":ee},{footer:h(()=>[n(m,{onClick:e[3]||(e[3]=i=>f.value=!1)},{default:h(()=>[...e[14]||(e[14]=[T("取消",-1)])]),_:1}),n(m,{type:"danger",onClick:we},{default:h(()=>[...e[15]||(e[15]=[T("删除",-1)])]),_:1})]),default:h(()=>[C("div",null,[e[13]||(e[13]=C("p",null,"确定要删除这个任务吗？",-1)),C("div",$t,[n(Ve,{modelValue:l.value,"onUpdate:modelValue":e[2]||(e[2]=i=>l.value=i)},{default:h(()=>[...e[12]||(e[12]=[T(" 同时删除文件 ",-1)])]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"]),n(Tt,{modelValue:c.value,"onUpdate:modelValue":e[5]||(e[5]=i=>c.value=i),tasks:p.value,"task-name":p.value.length===1?he(p.value[0]):void 0,"task-type":t.taskType,onConfirm:le},null,8,["modelValue","tasks","task-name","task-type"])])}}});const as=te(es,[["__scopeId","data-v-fb35e2c0"]]);export{as as default};
