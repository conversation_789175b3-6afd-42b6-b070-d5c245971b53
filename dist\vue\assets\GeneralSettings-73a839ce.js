import{d as H,N as Q,K as X,r as h,a as Z,j as z,L as x,g as ee,l as i,q as o,y as te,u as N,f as C,k as l,m as t,e as d,B as le,o as V,p as c,v as E,O as ae,E as r,A as ne,i as oe}from"./index-a49529e1.js";const se={class:"general-settings"},ie={class:"settings-header"},re={class:"settings-description"},ue={class:"form-tip"},de={class:"form-tip"},ge={class:"form-tip"},ce={style:{"font-weight":"600"}},me={class:"config-info"},fe={class:"config-item"},pe={class:"config-label"},ve={class:"config-value"},_e={class:"config-item"},he={class:"config-label"},be={class:"config-value"},ye={class:"config-item"},we={class:"config-label"},Ve={class:"config-value"},Te=H({__name:"GeneralSettings",setup($e){const{t:g}=Q(),m=X(),L=h(),b=h(!1),T=h(!1),y=h(!1),w=h(""),S=h(!1),p=h(null),n=Z({language:"zh-CN",theme:"light",refreshInterval:1e3,autoConnect:!0,minimizeToTray:!0,ui:{showStatusBar:!0,showToolbar:!0,defaultView:"downloading"}});z(()=>m.settings,e=>{Object.assign(n,{language:e.language,theme:e.theme,refreshInterval:e.refreshInterval,autoConnect:e.autoConnect,minimizeToTray:e.minimizeToTray,ui:{...e.ui}}),ae(()=>{S.value=!0})},{immediate:!0,deep:!0}),z(n,()=>{S.value&&P()},{deep:!0}),x(async()=>{if(await m.initialize(),window.electronAPI)try{p.value=await window.electronAPI.getConfigInfo(),console.log("Config info loaded:",p.value)}catch(e){console.error("Failed to get config info:",e)}});async function P(){if(!b.value){b.value=!0;try{await m.updateAriaNgSettings(n),await m.updateUIConfig(n.ui)}catch{r.error(g("settings.general.autoSaveFailed")||"自动保存失败")}finally{b.value=!1}}}async function R(){b.value=!0;try{await m.updateAriaNgSettings(n),await m.updateUIConfig(n.ui),r.success(g("settings.general.settingsSaved"))}catch{r.error("保存设置失败")}finally{b.value=!1}}async function A(){try{await ne.confirm(g("dialogs.deleteTask.confirmMessage"),g("dialogs.deleteTask.confirmDelete"),{confirmButtonText:g("common.confirm"),cancelButtonText:g("common.cancel"),type:"warning"}),await m.resetSettings(),r.success(g("settings.general.settingsReset"))}catch(e){e!=="cancel"&&r.error("重置设置失败")}}function F(){try{const e=m.exportSettings(),a=new Blob([e],{type:"application/json"}),u=URL.createObjectURL(a),v=document.createElement("a");v.href=u,v.download=`aria2-desktop-settings-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(v),v.click(),document.body.removeChild(v),URL.revokeObjectURL(u),r.success(g("settings.general.settingsExported"))}catch{r.error("导出设置失败")}}async function j(){if(!w.value.trim()){r.warning(g("dialogs.importSettings.inputRequired"));return}T.value=!0;try{await m.importSettings(w.value),y.value=!1,w.value="",r.success(g("settings.general.settingsImported"))}catch(e){r.error(g("dialogs.importSettings.importFailed",{error:e instanceof Error?e.message:"未知错误"}))}finally{T.value=!1}}function M(){O(n.theme)}function O(e){const a=e==="dark"||e==="auto"&&window.matchMedia("(prefers-color-scheme: dark)").matches;document.documentElement.classList.toggle("dark",a)}function G(){r.info(g("settings.general.languageChangeNotice"))}async function q(){if(!window.electronAPI||!p.value){r.warning("此功能仅在桌面版中可用");return}try{const e=await window.electronAPI.openPath(p.value.configDirectory);e.success?r.success("配置目录已打开"):r.error("打开配置目录失败："+e.error)}catch(e){r.error("打开配置目录失败"),console.error("Failed to open config directory:",e)}}return(e,a)=>{const u=d("el-option"),v=d("el-select"),f=d("el-form-item"),I=d("el-switch"),$=d("el-divider"),U=d("el-checkbox"),D=d("el-space"),k=d("el-radio"),J=d("el-radio-group"),_=d("el-button"),B=d("el-form"),K=d("el-input"),W=d("el-dialog"),Y=le("loading");return V(),ee("div",se,[i("div",ie,[i("h2",null,o(e.$t("settings.general.title")),1),i("p",re,o(e.$t("settings.general.description")),1)]),te((V(),C(B,{ref_key:"formRef",ref:L,model:n,"label-width":"150px",style:{"max-width":"600px"}},{default:l(()=>[t(f,{label:e.$t("settings.general.language")},{default:l(()=>[t(v,{modelValue:n.language,"onUpdate:modelValue":a[0]||(a[0]=s=>n.language=s),style:{width:"100%"},onChange:G},{default:l(()=>[t(u,{label:"简体中文",value:"zh-CN"}),t(u,{label:"繁体中文",value:"zh-TW"}),t(u,{label:"English",value:"en"})]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(f,{label:e.$t("settings.general.theme")},{default:l(()=>[t(v,{modelValue:n.theme,"onUpdate:modelValue":a[1]||(a[1]=s=>n.theme=s),style:{width:"100%"},onChange:M},{default:l(()=>[t(u,{label:e.$t("settings.general.lightTheme"),value:"light"},null,8,["label"]),t(u,{label:e.$t("settings.general.darkTheme"),value:"dark"},null,8,["label"]),t(u,{label:e.$t("settings.general.autoTheme"),value:"auto"},null,8,["label"])]),_:1},8,["modelValue"])]),_:1},8,["label"]),t(f,{label:e.$t("settings.general.refreshInterval")},{default:l(()=>[t(v,{modelValue:n.refreshInterval,"onUpdate:modelValue":a[2]||(a[2]=s=>n.refreshInterval=s),style:{width:"100%"}},{default:l(()=>[t(u,{label:"1秒",value:1e3}),t(u,{label:"2秒",value:2e3}),t(u,{label:"5秒",value:5e3}),t(u,{label:"10秒",value:1e4})]),_:1},8,["modelValue"]),i("div",ue,o(e.$t("settings.general.refreshIntervalTip")),1)]),_:1},8,["label"]),t(f,{label:e.$t("settings.general.autoConnect")},{default:l(()=>[t(I,{modelValue:n.autoConnect,"onUpdate:modelValue":a[3]||(a[3]=s=>n.autoConnect=s)},null,8,["modelValue"]),i("div",de,o(e.$t("settings.general.autoConnectTip")),1)]),_:1},8,["label"]),t(f,{label:e.$t("settings.general.minimizeToTray")},{default:l(()=>[t(I,{modelValue:n.minimizeToTray,"onUpdate:modelValue":a[4]||(a[4]=s=>n.minimizeToTray=s)},null,8,["modelValue"]),i("div",ge,o(e.$t("settings.general.minimizeToTrayTip")),1)]),_:1},8,["label"]),t($),t(f,{label:e.$t("settings.general.uiSettings")},{default:l(()=>[t(D,{direction:"vertical",style:{width:"100%"}},{default:l(()=>[t(U,{modelValue:n.ui.showStatusBar,"onUpdate:modelValue":a[5]||(a[5]=s=>n.ui.showStatusBar=s)},{default:l(()=>[c(o(e.$t("settings.general.showStatusBar")),1)]),_:1},8,["modelValue"]),t(U,{modelValue:n.ui.showToolbar,"onUpdate:modelValue":a[6]||(a[6]=s=>n.ui.showToolbar=s)},{default:l(()=>[c(o(e.$t("settings.general.showToolbar")),1)]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["label"]),t(f,{label:e.$t("settings.general.defaultView")},{default:l(()=>[t(J,{modelValue:n.ui.defaultView,"onUpdate:modelValue":a[7]||(a[7]=s=>n.ui.defaultView=s)},{default:l(()=>[t(k,{label:"downloading"},{default:l(()=>[c(o(e.$t("taskStatus.active")),1)]),_:1}),t(k,{label:"waiting"},{default:l(()=>[c(o(e.$t("taskStatus.waiting")),1)]),_:1}),t(k,{label:"stopped"},{default:l(()=>[c(o(e.$t("taskStatus.complete")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1},8,["label"]),t($),p.value?(V(),C(f,{key:0},{label:l(()=>[i("span",ce,o(e.$t("settings.general.configFileInfo")),1)]),default:l(()=>[i("div",me,[i("div",fe,[i("span",pe,o(e.$t("settings.general.configFilePath"))+":",1),i("span",ve,o(p.value.configFilePath),1)]),i("div",_e,[i("span",he,o(e.$t("settings.general.appDirectory"))+":",1),i("span",be,o(p.value.appDirectory),1)]),i("div",ye,[i("span",we,o(e.$t("settings.general.isPackaged"))+":",1),i("span",Ve,o(p.value.isPackaged?"Yes":"No (Development)"),1)])])]),_:1})):E("",!0),t($),t(f,null,{default:l(()=>[t(D,null,{default:l(()=>[t(_,{type:"primary",onClick:R,loading:b.value},{default:l(()=>[c(o(e.$t("settings.general.saveSettings")),1)]),_:1},8,["loading"]),t(_,{onClick:A,disabled:N(m).isLoading},{default:l(()=>[c(o(e.$t("settings.general.resetToDefault")),1)]),_:1},8,["disabled"]),t(_,{onClick:F},{default:l(()=>[c(o(e.$t("settings.general.exportSettings")),1)]),_:1}),t(_,{onClick:a[8]||(a[8]=s=>y.value=!0)},{default:l(()=>[c(o(e.$t("settings.general.importSettings")),1)]),_:1}),p.value?(V(),C(_,{key:0,onClick:q},{default:l(()=>[c(o(e.$t("settings.general.openConfigDirectory")),1)]),_:1})):E("",!0)]),_:1})]),_:1})]),_:1},8,["model"])),[[Y,N(m).isLoading]]),t(W,{modelValue:y.value,"onUpdate:modelValue":a[11]||(a[11]=s=>y.value=s),title:e.$t("dialogs.importSettings.title"),width:"500px"},{footer:l(()=>[t(_,{onClick:a[10]||(a[10]=s=>y.value=!1)},{default:l(()=>[c(o(e.$t("common.cancel")),1)]),_:1}),t(_,{type:"primary",onClick:j,loading:T.value},{default:l(()=>[c(o(e.$t("dialogs.importSettings.import")),1)]),_:1},8,["loading"])]),default:l(()=>[t(B,null,{default:l(()=>[t(f,{label:"设置文件"},{default:l(()=>[t(K,{modelValue:w.value,"onUpdate:modelValue":a[9]||(a[9]=s=>w.value=s),type:"textarea",rows:10,placeholder:e.$t("dialogs.importSettings.placeholder")},null,8,["modelValue","placeholder"])]),_:1})]),_:1})]),_:1},8,["modelValue","title"])])}}});const Ce=oe(Te,[["__scopeId","data-v-d86b615d"]]);export{Ce as default};
